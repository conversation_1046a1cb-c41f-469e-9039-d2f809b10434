import { Suspense } from 'react'
import { CheckCircleIcon, EnvelopeIcon } from '@heroicons/react/24/outline'
import Link from '@/components/Link'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Email Confirmed - آيات يومية',
  description: 'Your email subscription has been confirmed successfully.',
}

function ConfirmationContent() {
  return (
    <div className="mx-auto max-w-2xl px-4 py-16 text-center">
      <div className="rounded-lg border border-green-200 bg-green-50 p-8 dark:border-green-800 dark:bg-green-900/20">
        <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/40">
          <CheckCircleIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
        </div>

        <h1 className="mb-4 text-2xl font-bold text-green-900 dark:text-green-100">
          تم تأكيد اشتراكك بنجاح!
        </h1>

        <p className="mb-6 text-green-700 dark:text-green-300">
          شكراً لك على تأكيد اشتراكك في نشرتنا البريدية. ستبدأ في تلقي الآيات اليومية قريباً.
        </p>

        <div className="mb-6 rounded-lg bg-white p-4 dark:bg-gray-800">
          <div className="flex items-center justify-center gap-2 text-gray-600 dark:text-gray-400">
            <EnvelopeIcon className="h-5 w-5" />
            <span>ستصلك الآيات حسب التكرار الذي اخترته</span>
          </div>
        </div>

        <div className="space-y-3">
          <Link
            href="/"
            className="inline-block rounded-md bg-blue-600 px-6 py-3 font-medium text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none dark:focus:ring-offset-gray-800"
          >
            العودة للصفحة الرئيسية
          </Link>

          <div>
            <Link
              href="/email/preferences"
              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
            >
              إدارة تفضيلات الاشتراك
            </Link>
          </div>
        </div>
      </div>

      {/* English Version */}
      <div className="mt-8 rounded-lg border border-green-200 bg-green-50 p-8 dark:border-green-800 dark:bg-green-900/20">
        <h2 className="mb-4 text-xl font-bold text-green-900 dark:text-green-100">
          Email Confirmed Successfully!
        </h2>

        <p className="mb-6 text-green-700 dark:text-green-300">
          Thank you for confirming your subscription to our newsletter. You will start receiving
          daily verses soon.
        </p>

        <div className="space-y-3">
          <Link
            href="/"
            className="inline-block rounded-md bg-blue-600 px-6 py-3 font-medium text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none dark:focus:ring-offset-gray-800"
          >
            Back to Home
          </Link>

          <div>
            <Link
              href="/email/preferences"
              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
            >
              Manage Subscription Preferences
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function EmailConfirmedPage() {
  return (
    <Suspense
      fallback={
        <div className="mx-auto max-w-2xl px-4 py-16 text-center">
          <div className="animate-pulse">
            <div className="mx-auto mb-6 h-16 w-16 rounded-full bg-gray-200 dark:bg-gray-700"></div>
            <div className="mx-auto mb-4 h-8 w-64 rounded bg-gray-200 dark:bg-gray-700"></div>
            <div className="mx-auto mb-6 h-4 w-96 rounded bg-gray-200 dark:bg-gray-700"></div>
          </div>
        </div>
      }
    >
      <ConfirmationContent />
    </Suspense>
  )
}
