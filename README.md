# آيات يومية - موقع الآيات مع التطبيق العملي

موقع مبني على Next.js و Tailwind CSS لعرض الآيات القرآنية مع شرح عملي وتطبيق يومي. يدعم الموقع اللغة العربية بشكل كامل مع دعم RTL والخطوط العربية.

## المميزات

- 🌙 **آية اليوم**: عرض آية مختلفة كل يوم بشكل تلقائي
- 📖 **شرح عملي**: كل آية تحتوي على شرح وتطبيق عملي للحياة اليومية
- 🎯 **تتبع التقدم**: إمكانية تتبع الآيات المكتملة باستخدام localStorage
- 🏷️ **تصنيف بالمواضيع**: تنظيم الآيات حسب المواضيع والعلامات
- 🌍 **دعم RTL**: دعم كامل للغة العربية واتجاه النص من اليمين لليسار
- 📱 **تصميم متجاوب**: يعمل بشكل مثالي على جميع الأجهزة
- 🔍 **بحث متقدم**: إمكانية البحث في الآيات والمحتوى
- 🎨 **تصميم جميل**: واجهة مستخدم حديثة وجذابة
- ⚡ **أداء عالي**: مبني على Next.js 15 مع React Server Components

## التقنيات المستخدمة

- **Next.js 15** - إطار عمل React مع App Directory
- **Tailwind CSS 4** - إطار عمل CSS للتصميم
- **Contentlayer** - إدارة محتوى MDX
- **TypeScript** - لغة البرمجة
- **Cairo Font** - خط عربي جميل من Google Fonts
- **Heroicons** - مكتبة الأيقونات
- **Jest & Testing Library** - للاختبارات

## البدء السريع

### 1. تثبيت المتطلبات

```bash
# استنساخ المشروع
git clone https://github.com/your-username/verses-daily.git
cd verses-daily

# تثبيت الحزم
yarn install
```

### 2. تشغيل الموقع محلياً

```bash
yarn dev
```

افتح [http://localhost:3000](http://localhost:3000) في المتصفح لرؤية الموقع.

### 3. إضافة آية جديدة

أنشئ ملف MDX جديد في مجلد `content/verses/` باستخدام القالب التالي:

```mdx
---
title: 'عنوان الآية'
surah: 'اسم السورة'
ayah: رقم_الآية
slug: 'slug-unique'
date: '2025-08-21'
tags: ['موضوع1', 'موضوع2']
lesson: 'الدرس العملي: خطوة عملية يمكن تطبيقها اليوم'
reflection: 'سؤال للتفكر: سؤال يحفز على التأمل'
ogImage: '/og/image.png' # اختياري
---

## النص الكريم

**نص الآية هنا**

## الشرح والتفسير

شرح مفصل للآية...

## التطبيق العملي

كيفية تطبيق الآية في الحياة اليومية...
```

### 4. تشغيل الاختبارات

```bash
# تشغيل جميع الاختبارات
yarn test

# تشغيل الاختبارات مع المراقبة
yarn test:watch

# تشغيل الاختبارات مع تقرير التغطية
yarn test:coverage
```

## هيكل المشروع

```
├── app/                    # صفحات Next.js (App Directory)
│   ├── page.tsx           # الصفحة الرئيسية
│   ├── verses/            # صفحات الآيات
│   └── layout.tsx         # التخطيط الأساسي
├── components/            # مكونات React
│   ├── VerseLayout.tsx    # تخطيط عرض الآية
│   ├── VerseCard.tsx      # بطاقة الآية
│   ├── ApplyToday.tsx     # مكون التطبيق العملي
│   └── ReflectionPrompt.tsx # مكون التفكر
├── content/               # محتوى الموقع
│   └── verses/            # ملفات الآيات MDX
├── hooks/                 # React Hooks مخصصة
│   └── useProgress.js     # hook لتتبع التقدم
├── lib/                   # مكتبات مساعدة
│   └── verses.js          # وظائف إدارة الآيات
└── __tests__/             # ملفات الاختبارات
```

## المكونات الأساسية

### VerseLayout

مكون لعرض الآية مع جميع التفاصيل والمكونات التفاعلية.

### ApplyToday

مكون يعرض الدرس العملي مع إمكانية تتبع الإنجاز.

### useProgress Hook

Hook لإدارة حالة إنجاز الآيات في localStorage.

## إدارة المحتوى

### إضافة آية جديدة

1. أنشئ ملف MDX في `content/verses/`
2. استخدم القالب المحدد أعلاه
3. تأكد من وجود slug فريد
4. أضف العلامات المناسبة

### تنظيم الآيات

- استخدم العلامات (tags) لتصنيف الآيات حسب الموضوع
- اختر أسماء سور واضحة ومتسقة
- استخدم تواريخ منطقية لترتيب الآيات

## النشر

### Vercel (موصى به)

```bash
# ربط المشروع بـ Vercel
vercel

# نشر تلقائي عند push للـ main branch
git push origin main
```

### متغيرات البيئة

أنشئ ملف `.env.local`:

```env
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

## التخصيص

### تغيير الألوان

عدّل ملف `css/tailwind.css` لتخصيص الألوان:

```css
@theme {
  --color-primary-500: oklch(0.656 0.241 354.308);
  /* ألوان أخرى... */
}
```

### تخصيص الخطوط

يمكنك تغيير الخط العربي في `app/layout.tsx`:

```typescript
import { Amiri } from 'next/font/google'

const amiri = Amiri({
  subsets: ['arabic'],
  display: 'swap',
  variable: '--font-arabic',
})
```

## المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push للـ branch
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى فتح issue في GitHub.

---

_هذا المشروع مبني على [Tailwind Next.js Starter Blog](https://github.com/timlrx/tailwind-nextjs-starter-blog) وتم تخصيصه لعرض الآيات القرآنية._

- [jmalvarez.dev](https://www.jmalvarez.dev/) - José Miguel Álvarez's personal blog ([source code](https://github.com/josemiguel-alvarez/nextjs-blog))
- [justingosses.com](https://justingosses.com/) - Justin Gosses's personal website and blog ([source code](https://github.com/JustinGOSSES/justingosses-website))
- [raphaelchelly.com](https://www.raphaelchelly.com/) - Raphaël Chelly's personal website and blog ([source code](https://github.com/raphaelchelly/raph_www))
- [kaveh.page](https://kaveh.page) - Kaveh Tehrani's personal blog. Added tags directory, profile card, time-to-read on posts directory, etc.
- [drakerossman.com](https://drakerossman.com/) - Drake Rossman's blog about NixOS, Rust, Software Architecture and Engineering Management, as well as general musings.
- [meamenu.com](https://www.meamenu.com) - Landing page and product blog starting from this template. It also uses [framer-motion](https://www.framer.com/motion) for animations, custom layout templates, [waline](https://waline.js.org/en/) for blog comments and [primereact](https://primereact.org/) forms [Ita]
- [giovanni.orciuolo.it](https://giovanni.orciuolo.it) - Giovanni Orciuolo's personal website, blog and everything nerd.

## Motivation

I wanted to port my existing blog to Nextjs and Tailwind CSS but there was no easy out of the box template to use so I decided to create one. Design is adapted from [Tailwindlabs blog](https://github.com/tailwindlabs/blog.tailwindcss.com).

I wanted it to be nearly as feature-rich as popular blogging templates like [beautiful-jekyll](https://github.com/daattali/beautiful-jekyll) and [Hugo Academic](https://github.com/wowchemy/wowchemy-hugo-modules) but with the best of React's ecosystem and current web development's best practices.

## Features

- Next.js with Typescript
- [Contentlayer](https://www.contentlayer.dev/) to manage content logic
- Easy styling customization with [Tailwind 3.0](https://tailwindcss.com/blog/tailwindcss-v3) and primary color attribute
- [MDX - write JSX in markdown documents!](https://mdxjs.com/)
- Near perfect lighthouse score - [Lighthouse report](https://www.webpagetest.org/result/230805_BiDcBQ_4H7)
- Lightweight, 85kB first load JS
- Mobile-friendly view
- Light and dark theme
- Font optimization with [next/font](https://nextjs.org/docs/app/api-reference/components/font)
- Integration with [pliny](https://github.com/timlrx/pliny) that provides:
  - Multiple analytics options including [Umami](https://umami.is/), [Plausible](https://plausible.io/), [Simple Analytics](https://simpleanalytics.com/), Posthog and Google Analytics
  - Comments via [Giscus](https://github.com/laymonage/giscus), [Utterances](https://github.com/utterance/utterances) or Disqus
  - Newsletter API and component with support for Mailchimp, Buttondown, Convertkit, Klaviyo, Revue, Emailoctopus and Beehiiv
  - Command palette search with [Kbar](https://github.com/timc1/kbar) or Algolia
- Server-side syntax highlighting with line numbers and line highlighting via [rehype-prism-plus](https://github.com/timlrx/rehype-prism-plus)
- Math display supported via [KaTeX](https://katex.org/)
- Citation and bibliography support via [rehype-citation](https://github.com/timlrx/rehype-citation)
- [Github alerts](https://docs.github.com/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax#alerts) via [remark-github-blockquote-alert](https://github.com/jaywcjlove/remark-github-blockquote-alert)
- Automatic image optimization via [next/image](https://nextjs.org/docs/basic-features/image-optimization)
- Support for tags - each unique tag will be its own page
- Support for multiple authors
- 3 different blog layouts
- 2 different blog listing layouts
- Support for nested routing of blog posts
- Projects page
- Preconfigured security headers
- SEO friendly with RSS feed, sitemaps and more!

## Sample posts

- [A markdown guide](https://tailwind-nextjs-starter-blog.vercel.app/blog/github-markdown-guide)
- [Learn more about images in Next.js](https://tailwind-nextjs-starter-blog.vercel.app/blog/guide-to-using-images-in-nextjs)
- [A tour of math typesetting](https://tailwind-nextjs-starter-blog.vercel.app/blog/deriving-ols-estimator)
- [Simple MDX image grid](https://tailwind-nextjs-starter-blog.vercel.app/blog/pictures-of-canada)
- [Example of long prose](https://tailwind-nextjs-starter-blog.vercel.app/blog/the-time-machine)
- [Example of Nested Route Post](https://tailwind-nextjs-starter-blog.vercel.app/blog/nested-route/introducing-multi-part-posts-with-nested-routing)

## Quick Start Guide

1. Clone the repo

```bash
npx degit 'timlrx/tailwind-nextjs-starter-blog'
```

2. Personalize `siteMetadata.js` (site related information)
3. Modify the content security policy in `next.config.js` if you want to use
   other analytics provider or a commenting solution other than giscus.
4. Personalize `authors/default.md` (main author)
5. Modify `projectsData.ts`
6. Modify `headerNavLinks.ts` to customize navigation links
7. Add blog posts
8. Deploy on Vercel

## Installation

```bash
yarn
```

Please note, that if you are using Windows, you may need to run:

```bash
$env:PWD = $(Get-Location).Path
```

## Development

First, run the development server:

```bash
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

Edit the layout in `app` or content in `data`. With live reloading, the pages auto-updates as you edit them.

## Extend / Customize

`data/siteMetadata.js` - contains most of the site related information which should be modified for a user's need.

`data/authors/default.md` - default author information (required). Additional authors can be added as files in `data/authors`.

`data/projectsData.js` - data used to generate styled card on the projects page.

`data/headerNavLinks.js` - navigation links.

`data/logo.svg` - replace with your own logo.

`data/blog` - replace with your own blog posts.

`public/static` - store assets such as images and favicons.

`tailwind.config.js` and `css/tailwind.css` - tailwind configuration and stylesheet which can be modified to change the overall look and feel of the site.

`css/prism.css` - controls the styles associated with the code blocks. Feel free to customize it and use your preferred prismjs theme e.g. [prism themes](https://github.com/PrismJS/prism-themes).

`contentlayer.config.ts` - configuration for Contentlayer, including definition of content sources and MDX plugins used. See [Contentlayer documentation](https://www.contentlayer.dev/docs/getting-started) for more information.

`components/MDXComponents.js` - pass your own JSX code or React component by specifying it over here. You can then use them directly in the `.mdx` or `.md` file. By default, a custom link, `next/image` component, table of contents component and Newsletter form are passed down. Note that the components should be default exported to avoid [existing issues with Next.js](https://github.com/vercel/next.js/issues/51593).

`layouts` - main templates used in pages:

- There are currently 3 post layouts available: `PostLayout`, `PostSimple` and `PostBanner`. `PostLayout` is the default 2 column layout with meta and author information. `PostSimple` is a simplified version of `PostLayout`, while `PostBanner` features a banner image.
- There are 2 blog listing layouts: `ListLayout`, the layout used in version 1 of the template with a search bar and `ListLayoutWithTags`, currently used in version 2, which omits the search bar but includes a sidebar with information on the tags.

`app` - pages to route to. Read the [Next.js documentation](https://nextjs.org/docs/app) for more information.

`next.config.js` - configuration related to Next.js. You need to adapt the Content Security Policy if you want to load scripts, images etc. from other domains.

## Post

Content is modelled using [Contentlayer](https://www.contentlayer.dev/), which allows you to define your own content schema and use it to generate typed content objects. See [Contentlayer documentation](https://www.contentlayer.dev/docs/getting-started) for more information.

### Frontmatter

Frontmatter follows [Hugo's standards](https://gohugo.io/content-management/front-matter/).

Please refer to `contentlayer.config.ts` for an up to date list of supported fields. The following fields are supported:

```
title (required)
date (required)
tags (optional)
lastmod (optional)
draft (optional)
summary (optional)
images (optional)
authors (optional list which should correspond to the file names in `data/authors`. Uses `default` if none is specified)
layout (optional list which should correspond to the file names in `data/layouts`)
canonicalUrl (optional, canonical url for the post for SEO)
```

Here's an example of a post's frontmatter:

```
---
title: 'Introducing Tailwind Nexjs Starter Blog'
date: '2021-01-12'
lastmod: '2021-01-18'
tags: ['next-js', 'tailwind', 'guide']
draft: false
summary: 'Looking for a performant, out of the box template, with all the best in web technology to support your blogging needs? Checkout the Tailwind Nextjs Starter Blog template.'
images: ['/static/images/canada/mountains.jpg', '/static/images/canada/toronto.jpg']
authors: ['default', 'sparrowhawk']
layout: PostLayout
canonicalUrl: https://tailwind-nextjs-starter-blog.vercel.app/blog/introducing-tailwind-nextjs-starter-blog
---
```

## Deploy

### GitHub Pages

A [`pages.yml`](.github/workflows/pages.yml) workflow is already provided. Simply select "GitHub Actions" in: `Settings > Pages > Build and deployment > Source`.

### Vercel

The easiest way to deploy the template is to deploy on [Vercel](https://vercel.com). Check out the [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

### Netlify

[Netlify](https://www.netlify.com/)’s Next.js runtime configures enables key Next.js functionality on your website without the need for additional configurations. Netlify generates serverless functions that will handle Next.js functionalities such as server-side rendered (SSR) pages, incremental static regeneration (ISR), `next/images`, etc.

See [Next.js on Netlify](https://docs.netlify.com/integrations/frameworks/next-js/overview/#next-js-runtime) for suggested configuration values and more details.

### Static hosting services (GitHub Pages / S3 / Firebase etc.)

Run:

```sh
$ EXPORT=1 UNOPTIMIZED=1 yarn build
```

Then, deploy the generated `out` folder or run `npx serve out` it locally.

> [!IMPORTANT]
> If deploying with a URL base path, like https://example.org/myblog you need an extra `BASE_PATH` shell-var to the build command:
>
> ```sh
> $ EXPORT=1 UNOPTIMIZED=1 BASE_PATH=/myblog yarn build
> ```
>
> => In your code, `${process.env.BASE_PATH || ''}/robots.txt` will print `"/myblog/robots.txt"` in the `out` build (or only `/robots.txt` if `yarn dev`, ie: on localhost:3000)

> [!TIP]
> Alternatively to `UNOPTIMIZED=1`, to continue using `next/image`, you can use an alternative image optimization provider such as Imgix, Cloudinary or Akamai. See [image optimization documentation](https://nextjs.org/docs/app/building-your-application/deploying/static-exports#image-optimization) for more details.

Consider removing the following features that cannot be used in a static build:

1. Comment out `headers()` from `next.config.js`.
2. Remove `api` folder and components which call the server-side function such as the Newsletter component. Not technically required and the site will build successfully, but the APIs cannot be used as they are server-side functions.

## Licence

[MIT](https://github.com/timlrx/tailwind-nextjs-starter-blog/blob/main/LICENSE) © [Timothy Lin](https://www.timlrx.com)
