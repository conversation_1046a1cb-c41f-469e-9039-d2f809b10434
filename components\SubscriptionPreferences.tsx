'use client'

import { useState, useEffect } from 'react'
import { useTranslation } from '@/hooks/useTranslation'
import {
  CogIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  EyeIcon,
  EyeSlashIcon,
} from '@heroicons/react/24/outline'

interface SubscriptionData {
  email: string
  frequency: 'daily' | 'weekly'
  language: 'ar' | 'en'
  isActive: boolean
  isConfirmed: boolean
  subscribedAt: string
}

export default function SubscriptionPreferences() {
  const { t, language } = useTranslation()
  const [email, setEmail] = useState('')
  const [subscription, setSubscription] = useState<SubscriptionData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [status, setStatus] = useState<'idle' | 'success' | 'error' | 'not_found'>('idle')
  const [message, setMessage] = useState('')
  const [showPreferences, setShowPreferences] = useState(false)

  const handleLookup = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email.trim()) {
      setStatus('error')
      setMessage(t('preferences.emailRequired'))
      return
    }

    setIsLoading(true)
    setStatus('idle')

    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'get_preferences',
          email: email.trim(),
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setSubscription(data.subscription)
        setShowPreferences(true)
        setStatus('success')
        setMessage(t('preferences.found'))
      } else {
        setStatus('not_found')
        setMessage(data.error || t('preferences.notFound'))
        setSubscription(null)
        setShowPreferences(false)
      }
    } catch (error) {
      setStatus('error')
      setMessage(t('preferences.networkError'))
      setSubscription(null)
      setShowPreferences(false)
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdatePreferences = async (
    updates: Partial<Pick<SubscriptionData, 'frequency' | 'language' | 'isActive'>>
  ) => {
    if (!subscription) return

    setIsSaving(true)

    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'update_preferences',
          email: subscription.email,
          ...updates,
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setSubscription({ ...subscription, ...updates })
        setStatus('success')
        setMessage(t('preferences.updateSuccess'))
      } else {
        setStatus('error')
        setMessage(data.error || t('preferences.updateError'))
      }
    } catch (error) {
      setStatus('error')
      setMessage(t('preferences.networkError'))
    } finally {
      setIsSaving(false)
    }
  }

  const resetStatus = () => {
    setStatus('idle')
    setMessage('')
  }

  return (
    <div className="mx-auto max-w-2xl">
      <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
        <div className="mb-6 text-center">
          <h2 className="mb-2 flex items-center justify-center gap-2 text-2xl font-bold text-gray-900 dark:text-gray-100">
            <CogIcon className="h-6 w-6" />
            {t('preferences.title')}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">{t('preferences.description')}</p>
        </div>

        {/* Email Lookup Form */}
        <form onSubmit={handleLookup} className="mb-6">
          <div className="flex gap-3">
            <input
              type="email"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value)
                resetStatus()
              }}
              placeholder={t('preferences.emailPlaceholder')}
              className="flex-1 rounded-md border border-gray-300 px-4 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              disabled={isLoading}
            />
            <button
              type="submit"
              disabled={isLoading}
              className="flex items-center gap-2 rounded-md bg-blue-600 px-4 py-2 font-medium text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none disabled:opacity-50 dark:focus:ring-offset-gray-800"
            >
              {isLoading ? (
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
              ) : (
                <EyeIcon className="h-4 w-4" />
              )}
              {t('preferences.lookup')}
            </button>
          </div>
        </form>

        {/* Status Messages */}
        {status !== 'idle' && (
          <div
            className={`mb-6 flex items-center gap-2 rounded-md p-3 ${
              status === 'success'
                ? 'bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400'
                : status === 'not_found'
                  ? 'bg-yellow-50 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400'
                  : 'bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-400'
            }`}
          >
            {status === 'success' ? (
              <CheckCircleIcon className="h-5 w-5" />
            ) : (
              <ExclamationCircleIcon className="h-5 w-5" />
            )}
            {message}
          </div>
        )}

        {/* Subscription Preferences */}
        {showPreferences && subscription && (
          <div className="space-y-6">
            <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-600 dark:bg-gray-700">
              <h3 className="mb-4 font-semibold text-gray-900 dark:text-gray-100">
                {t('preferences.currentSettings')}
              </h3>

              <div className="grid gap-4 sm:grid-cols-2">
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('preferences.status')}
                  </label>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handleUpdatePreferences({ isActive: !subscription.isActive })}
                      disabled={isSaving}
                      className={`flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium ${
                        subscription.isActive
                          ? 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400'
                          : 'bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900/20 dark:text-red-400'
                      }`}
                    >
                      {subscription.isActive ? (
                        <>
                          <CheckCircleIcon className="h-4 w-4" />
                          {t('preferences.active')}
                        </>
                      ) : (
                        <>
                          <EyeSlashIcon className="h-4 w-4" />
                          {t('preferences.inactive')}
                        </>
                      )}
                    </button>
                  </div>
                </div>

                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('preferences.frequency')}
                  </label>
                  <select
                    value={subscription.frequency}
                    onChange={(e) =>
                      handleUpdatePreferences({ frequency: e.target.value as 'daily' | 'weekly' })
                    }
                    disabled={isSaving}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="daily">{t('preferences.daily')}</option>
                    <option value="weekly">{t('preferences.weekly')}</option>
                  </select>
                </div>

                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('preferences.language')}
                  </label>
                  <select
                    value={subscription.language}
                    onChange={(e) =>
                      handleUpdatePreferences({ language: e.target.value as 'ar' | 'en' })
                    }
                    disabled={isSaving}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="ar">{t('preferences.arabic')}</option>
                    <option value="en">{t('preferences.english')}</option>
                  </select>
                </div>

                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t('preferences.subscribedAt')}
                  </label>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {new Date(subscription.subscribedAt).toLocaleDateString(
                      language === 'ar' ? 'ar-SA' : 'en-US'
                    )}
                  </div>
                </div>
              </div>

              {!subscription.isConfirmed && (
                <div className="mt-4 rounded-md bg-yellow-50 p-3 dark:bg-yellow-900/20">
                  <div className="flex items-center gap-2 text-yellow-700 dark:text-yellow-400">
                    <ExclamationCircleIcon className="h-5 w-5" />
                    <span className="text-sm font-medium">{t('preferences.unconfirmed')}</span>
                  </div>
                </div>
              )}
            </div>

            {/* Unsubscribe Section */}
            <div className="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
              <h4 className="mb-2 font-medium text-red-900 dark:text-red-400">
                {t('preferences.unsubscribeTitle')}
              </h4>
              <p className="mb-3 text-sm text-red-700 dark:text-red-300">
                {t('preferences.unsubscribeDescription')}
              </p>
              <button
                onClick={() => handleUpdatePreferences({ isActive: false })}
                disabled={isSaving || !subscription.isActive}
                className="rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:outline-none disabled:opacity-50 dark:focus:ring-offset-gray-800"
              >
                {t('preferences.unsubscribe')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
