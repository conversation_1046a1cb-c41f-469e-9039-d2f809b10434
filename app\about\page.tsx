'use client'

import { BookOpenIcon, HeartIcon, StarIcon, UserGroupIcon } from '@heroicons/react/24/outline'
import { useTranslation } from '@/hooks/useTranslation'
import Link from '@/components/Link'

export default function AboutPage() {
  const { t } = useTranslation()
  return (
    <div className="mx-auto max-w-4xl px-4 py-8">
      {/* Header */}
      <div className="mb-12 text-center">
        <h1 className="mb-4 text-4xl font-bold text-gray-900 dark:text-gray-100">
          {t('about.title')}
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-400">{t('about.subtitle')}</p>
      </div>

      {/* Mission Section */}
      <div className="mb-16">
        <div className="rounded-lg border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-8 dark:border-blue-700 dark:from-blue-900/20 dark:to-indigo-900/20">
          <div className="mb-6 flex items-center">
            <BookOpenIcon className="ml-3 h-8 w-8 text-blue-600 dark:text-blue-400" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {t('about.mission')}
            </h2>
          </div>
          <p className="text-lg leading-relaxed text-gray-700 dark:text-gray-300">
            {t('about.missionText')}
          </p>
        </div>
      </div>

      {/* Features Grid */}
      <div className="mb-16">
        <h2 className="mb-8 text-center text-3xl font-bold text-gray-900 dark:text-gray-100">
          {t('about.features')}
        </h2>
        <div className="grid gap-8 md:grid-cols-2">
          {/* Daily Verses */}
          <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <div className="mb-4 flex items-center">
              <BookOpenIcon className="ml-3 h-6 w-6 text-emerald-600 dark:text-emerald-400" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                {t('about.dailyVerse')}
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400">{t('about.dailyVerseDesc')}</p>
          </div>

          {/* Personal Path */}
          <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <div className="mb-4 flex items-center">
              <StarIcon className="ml-3 h-6 w-6 text-amber-600 dark:text-amber-400" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                المسار الشخصي
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              تحديات 7 أيام لتطبيق القيم القرآنية مثل الشكر والتوكل والصبر في حياتك العملية.
            </p>
          </div>

          {/* Progress Tracking */}
          <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <div className="mb-4 flex items-center">
              <HeartIcon className="ml-3 h-6 w-6 text-red-600 dark:text-red-400" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                تتبع التقدم
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              نظام ذكي لتتبع إنجازاتك اليومية ومساعدتك على بناء عادات إيجابية مستدامة.
            </p>
          </div>

          {/* Reflection */}
          <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <div className="mb-4 flex items-center">
              <UserGroupIcon className="ml-3 h-6 w-6 text-purple-600 dark:text-purple-400" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                التأمل والتفكر
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400">
              أسئلة تحفز على التفكر العميق في معاني الآيات وكيفية تطبيقها في مواقف الحياة المختلفة.
            </p>
          </div>
        </div>
      </div>

      {/* How It Works */}
      <div className="mb-16">
        <h2 className="mb-8 text-center text-3xl font-bold text-gray-900 dark:text-gray-100">
          كيف يعمل الموقع
        </h2>
        <div className="space-y-6">
          <div className="flex items-start">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400">
              1
            </div>
            <div className="mr-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                ابدأ بآية اليوم
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                كل يوم، ستجد آية جديدة مع شرح مبسط ودرس عملي يمكنك تطبيقه.
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400">
              2
            </div>
            <div className="mr-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                انضم لتحدي 7 أيام
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                اختر مساراً شخصياً (مثل الشكر أو التوكل) وابدأ رحلة 7 أيام لتطبيق هذه القيمة.
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400">
              3
            </div>
            <div className="mr-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">تابع تقدمك</h3>
              <p className="text-gray-600 dark:text-gray-400">
                سجل إنجازاتك اليومية وشاهد تقدمك عبر الإحصائيات والمتتاليات.
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400">
              4
            </div>
            <div className="mr-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">تأمل وتفكر</h3>
              <p className="text-gray-600 dark:text-gray-400">
                استخدم أسئلة التفكر لتعميق فهمك وربط الآيات بتجاربك الشخصية.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Vision */}
      <div className="mb-16">
        <div className="rounded-lg border border-emerald-200 bg-gradient-to-r from-emerald-50 to-green-50 p-8 dark:border-emerald-700 dark:from-emerald-900/20 dark:to-green-900/20">
          <h2 className="mb-4 text-2xl font-bold text-gray-900 dark:text-gray-100">رؤيتنا</h2>
          <p className="text-lg leading-relaxed text-gray-700 dark:text-gray-300">
            نسعى لأن نكون الجسر الذي يربط بين تعاليم القرآن الكريم والحياة العملية، مساعدين المسلمين
            في جميع أنحاء العالم على تطبيق القيم الإسلامية في حياتهم اليومية بطريقة عملية ومستدامة.
          </p>
        </div>
      </div>

      {/* Contact */}
      <div className="text-center">
        <h2 className="mb-4 text-2xl font-bold text-gray-900 dark:text-gray-100">
          {t('about.startToday')}
        </h2>
        <p className="mb-6 text-gray-600 dark:text-gray-400">{t('about.startTodayDesc')}</p>
        <div className="flex justify-center gap-4">
          <Link
            href="/"
            className="rounded-lg bg-blue-600 px-6 py-3 font-medium text-white transition-colors hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
          >
            {t('about.dailyVerseBtn')}
          </Link>
          <Link
            href="/challenges"
            className="rounded-lg bg-amber-600 px-6 py-3 font-medium text-white transition-colors hover:bg-amber-700 dark:bg-amber-500 dark:hover:bg-amber-600"
          >
            {t('about.personalPathBtn')}
          </Link>
        </div>
      </div>
    </div>
  )
}
