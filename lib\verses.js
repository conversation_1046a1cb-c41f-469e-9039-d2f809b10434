import { allVerses } from 'contentlayer/generated'
import { sortPosts } from 'pliny/utils/contentlayer'

/**
 * Get all verses sorted by date (newest first)
 * @returns {Array} Array of all verse objects
 */
export function getAllVerses() {
  const publishedVerses = allVerses.filter((verse) => verse.draft !== true)
  return sortPosts(publishedVerses)
}

/**
 * Get a specific verse by its slug
 * @param {string} slug - The verse slug
 * @returns {Object|null} The verse object or null if not found
 */
export function getVerseBySlug(slug) {
  const verses = getAllVerses()
  return verses.find((verse) => verse.slug === slug) || null
}

/**
 * Get today's verse based on a daily rotation
 * Uses the current date to determine which verse to show
 * @returns {Object|null} Today's verse object or null if no verses available
 */
export function getTodaysVerse() {
  const verses = getAllVerses()

  if (verses.length === 0) {
    return null
  }

  // Get the number of days since epoch
  const today = new Date()
  const daysSinceEpoch = Math.floor(today.getTime() / (1000 * 60 * 60 * 24))

  // Use modulo to cycle through verses
  const verseIndex = daysSinceEpoch % verses.length

  return verses[verseIndex]
}

/**
 * Get verses by tag
 * @param {string} tag - The tag to filter by
 * @returns {Array} Array of verses with the specified tag
 */
export function getVersesByTag(tag) {
  const verses = getAllVerses()
  return verses.filter(
    (verse) => verse.tags && verse.tags.some((t) => t.toLowerCase() === tag.toLowerCase())
  )
}

/**
 * Get all unique tags from verses
 * @returns {Array} Array of unique tags
 */
export function getAllTags() {
  const verses = getAllVerses()
  const tags = new Set()

  verses.forEach((verse) => {
    if (verse.tags) {
      verse.tags.forEach((tag) => tags.add(tag))
    }
  })

  return Array.from(tags).sort()
}

/**
 * Get verses by surah
 * @param {string} surah - The surah name
 * @returns {Array} Array of verses from the specified surah
 */
export function getVersesBySurah(surah) {
  const verses = getAllVerses()
  return verses.filter((verse) => verse.surah && verse.surah.toLowerCase() === surah.toLowerCase())
}

/**
 * Get random verse
 * @returns {Object|null} A random verse object or null if no verses available
 */
export function getRandomVerse() {
  const verses = getAllVerses()

  if (verses.length === 0) {
    return null
  }

  const randomIndex = Math.floor(Math.random() * verses.length)
  return verses[randomIndex]
}

/**
 * Search verses by title, content, or tags
 * @param {string} query - The search query
 * @returns {Array} Array of matching verses
 */
export function searchVerses(query) {
  const verses = getAllVerses()
  const searchTerm = query.toLowerCase()

  return verses.filter((verse) => {
    const titleMatch = verse.title && verse.title.toLowerCase().includes(searchTerm)
    const surahMatch = verse.surah && verse.surah.toLowerCase().includes(searchTerm)
    const lessonMatch = verse.lesson && verse.lesson.toLowerCase().includes(searchTerm)
    const reflectionMatch = verse.reflection && verse.reflection.toLowerCase().includes(searchTerm)
    const tagMatch = verse.tags && verse.tags.some((tag) => tag.toLowerCase().includes(searchTerm))

    return titleMatch || surahMatch || lessonMatch || reflectionMatch || tagMatch
  })
}
