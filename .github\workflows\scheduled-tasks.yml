name: Scheduled Tasks

on:
  schedule:
    # Daily reminder at 22:14 UTC (equivalent to "14 22 * * *" in Vercel)
    - cron: '34 22 * * *'
    # Weekly reminder on Sunday at 22:14 UTC (equivalent to "14 22 * * 0" in Vercel)
    - cron: '30 22 * * 0'
    # Daily revalidation at 00:00 UTC (equivalent to "0 0 * * *" in Vercel)
    - cron: '0 0 * * *'
  workflow_dispatch:  # Allow manual triggering

jobs:
  run-tasks:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18.x'
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Run daily email task
        if: github.event.schedule == '34 22 * * *'
        run: |
          curl -X POST "${{ secrets.VERCEL_URL }}/api/email/notify" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
            -d '{"type": "daily_reminder"}'
        continue-on-error: true

      - name: Run weekly email task
        if: github.event.schedule == '34 22 * * 0'
        run: |
          curl -X POST "${{ secrets.VERCEL_URL }}/api/email/notify" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
            -d '{"type": "weekly_reminder"}'
        continue-on-error: true

      - name: Run revalidation task
        if: github.event.schedule == '0 0 * * *'
        run: |
          curl -X GET "${{ secrets.VERCEL_URL }}/api/cron/revalidate" \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}"
        continue-on-error: true

      - name: Notify on failure
        if: failure()
        run: |
          curl -X POST "${{ secrets.SLACK_WEBHOOK_URL }}" \
            -H 'Content-type: application/json' \
            --data '{
              "text": "GitHub Actions scheduled task failed",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": "GitHub Actions Scheduled Task Failed"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*Repository:* ${{ github.repository }}\n*Workflow:* ${{ github.workflow }}\n*Run Number:* ${{ github.run_number }}\n*Run Attempt:* ${{ github.run_attempt }}\n*Event:* ${{ github.event_name }}\n*Ref:* ${{ github.ref }}\n*SHA:* ${{ github.sha }}"
                  }
                },
                {
                  "type": "actions",
                  "elements": [
                    {
                      "type": "button",
                      "text": {
                        "type": "plain_text",
                        "text": "View Run Details"
                      },
                      "url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                    }
                  ]
                }
              ]
            }'
        continue-on-error: true
