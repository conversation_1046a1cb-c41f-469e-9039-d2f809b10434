'use client'

import ChallengeProgress from './ChallengeProgress'
import ChallengeEnrollment from './ChallengeEnrollment'

interface HomeChallengeSectionProps {
  verseCategory?: string
}

export default function HomeChallengeSection({ verseCategory }: HomeChallengeSectionProps) {
  return (
    <section className="mb-16">
      <ChallengeProgress />
      <div className="mt-6">
        <ChallengeEnrollment
          verseCategory={verseCategory}
          onEnroll={() => (window.location.href = '/challenges')}
        />
      </div>
    </section>
  )
}
