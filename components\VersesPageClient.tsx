'use client'

import VerseCard from '@/components/VerseCard'
import Tag from '@/components/Tag'
import Link from '@/components/Link'
import { useTranslation } from '@/hooks/useTranslation'
import { CoreContent } from 'pliny/utils/contentlayer'
import { Verse } from 'contentlayer/generated'

interface VersesPageClientProps {
  verses: CoreContent<Verse>[]
  tags: string[]
}

export default function VersesPageClient({ verses, tags }: VersesPageClientProps) {
  const { t } = useTranslation()

  return (
    <div className="mx-auto max-w-6xl px-4 py-8">
      {/* Header */}
      <div className="mb-12 text-center">
        <h1 className="mb-4 text-4xl font-bold text-gray-900 dark:text-gray-100">
          {t('verses.title')}
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          {t('verses.description')} ({verses.length} {t('verses.count')})
        </p>
      </div>

      {/* Tags Filter */}
      {tags.length > 0 && (
        <div className="mb-8">
          <h2 className="mb-4 text-xl font-semibold text-gray-900 dark:text-gray-100">
            {t('verses.browseByTopic')}
          </h2>
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <Link key={tag} href={`/tags/${tag}`}>
                <Tag text={tag} />
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Verses Grid */}
      {verses.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {verses.map((verse) => (
            <VerseCard
              key={verse.slug}
              slug={verse.slug}
              title={verse.title}
              surah={verse.surah}
              ayah={verse.ayah}
              date={verse.date}
              tags={verse.tags}
              lesson={verse.lesson}
              reflection={verse.reflection}
            />
          ))}
        </div>
      ) : (
        <div className="py-12 text-center">
          <h3 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
            {t('verses.noVerses')}
          </h3>
          <p className="text-gray-600 dark:text-gray-400">{t('verses.noVersesDesc')}</p>
        </div>
      )}

      {/* Back to Home */}
      <div className="mt-12 text-center">
        <Link
          href="/"
          className="inline-flex items-center text-blue-600 transition-colors hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
        >
          {t('verses.backToHome')}
        </Link>
      </div>
    </div>
  )
}
