import { Suspense } from 'react'
import { CheckCircleIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline'
import Link from '@/components/Link'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Unsubscribed - آيات يومية',
  description: 'You have been unsubscribed from our newsletter.',
}

function UnsubscribeContent() {
  return (
    <div className="mx-auto max-w-2xl px-4 py-16 text-center">
      <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-8 dark:border-yellow-800 dark:bg-yellow-900/20">
        <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/40">
          <ExclamationCircleIcon className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
        </div>

        <h1 className="mb-4 text-2xl font-bold text-yellow-900 dark:text-yellow-100">
          تم إلغاء اشتراكك
        </h1>

        <p className="mb-6 text-yellow-700 dark:text-yellow-300">
          تم إلغاء اشتراكك في النشرة البريدية بنجاح. لن تتلقى المزيد من رسائل البريد الإلكتروني منا.
        </p>

        <div className="mb-6 rounded-lg bg-white p-4 dark:bg-gray-800">
          <p className="text-gray-600 dark:text-gray-400">
            إذا غيرت رأيك، يمكنك الاشتراك مرة أخرى في أي وقت من خلال النموذج في الصفحة الرئيسية.
          </p>
        </div>

        <div className="space-y-3">
          <Link
            href="/"
            className="inline-block rounded-md bg-blue-600 px-6 py-3 font-medium text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none dark:focus:ring-offset-gray-800"
          >
            العودة للصفحة الرئيسية
          </Link>

          <div>
            <Link
              href="/#newsletter"
              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
            >
              الاشتراك مرة أخرى
            </Link>
          </div>
        </div>
      </div>

      {/* English Version */}
      <div className="mt-8 rounded-lg border border-yellow-200 bg-yellow-50 p-8 dark:border-yellow-800 dark:bg-yellow-900/20">
        <h2 className="mb-4 text-xl font-bold text-yellow-900 dark:text-yellow-100">
          Successfully Unsubscribed
        </h2>

        <p className="mb-6 text-yellow-700 dark:text-yellow-300">
          You have been successfully unsubscribed from our newsletter. You will no longer receive
          emails from us.
        </p>

        <div className="mb-6 rounded-lg bg-white p-4 dark:bg-gray-800">
          <p className="text-gray-600 dark:text-gray-400">
            If you change your mind, you can subscribe again anytime using the form on our homepage.
          </p>
        </div>

        <div className="space-y-3">
          <Link
            href="/"
            className="inline-block rounded-md bg-blue-600 px-6 py-3 font-medium text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none dark:focus:ring-offset-gray-800"
          >
            Back to Home
          </Link>

          <div>
            <Link
              href="/#newsletter"
              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
            >
              Subscribe Again
            </Link>
          </div>
        </div>
      </div>

      {/* Feedback Section */}
      <div className="mt-8 rounded-lg border border-gray-200 bg-gray-50 p-6 dark:border-gray-700 dark:bg-gray-800">
        <h3 className="mb-3 text-lg font-semibold text-gray-900 dark:text-gray-100">
          نود سماع رأيك / We'd Love Your Feedback
        </h3>
        <p className="mb-4 text-gray-600 dark:text-gray-400">
          ساعدنا في تحسين خدمتنا بإخبارنا عن سبب إلغاء الاشتراك.
        </p>
        <p className="mb-4 text-gray-600 dark:text-gray-400">
          Help us improve our service by telling us why you unsubscribed.
        </p>
        <Link
          href={`mailto:${process.env.NEXT_PUBLIC_CONTACT_EMAIL || '<EMAIL>'}?subject=Unsubscribe Feedback`}
          className="inline-block rounded-md bg-gray-600 px-4 py-2 text-white hover:bg-gray-700 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:outline-none dark:focus:ring-offset-gray-800"
        >
          إرسال ملاحظات / Send Feedback
        </Link>
      </div>
    </div>
  )
}

export default function EmailUnsubscribedPage() {
  return (
    <Suspense
      fallback={
        <div className="mx-auto max-w-2xl px-4 py-16 text-center">
          <div className="animate-pulse">
            <div className="mx-auto mb-6 h-16 w-16 rounded-full bg-gray-200 dark:bg-gray-700"></div>
            <div className="mx-auto mb-4 h-8 w-64 rounded bg-gray-200 dark:bg-gray-700"></div>
            <div className="mx-auto mb-6 h-4 w-96 rounded bg-gray-200 dark:bg-gray-700"></div>
          </div>
        </div>
      }
    >
      <UnsubscribeContent />
    </Suspense>
  )
}
