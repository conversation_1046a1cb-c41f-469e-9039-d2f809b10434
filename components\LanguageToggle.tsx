'use client'

import { useLanguage } from '@/contexts/LanguageContext'

export default function LanguageToggle() {
  const { language, toggleLanguage } = useLanguage()

  return (
    <div className="group relative">
      <button
        onClick={toggleLanguage}
        className="relative flex h-10 w-20 items-center rounded-full border-2 border-gray-300 bg-white p-1 transition-all duration-300 hover:border-blue-400 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:hover:border-blue-500"
        aria-label={`Switch to ${language === 'ar' ? 'English' : 'Arabic'}`}
      >
        {/* Background slider */}
        <div
          className={`absolute inset-1 w-8 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 shadow-md transition-transform duration-300 ease-in-out ${
            language === 'en' ? 'translate-x-8' : 'translate-x-0'
          }`}
        />

        {/* Arabic side */}
        <div className="relative z-10 flex h-8 w-8 items-center justify-center">
          <span
            className={`text-xs font-bold transition-all duration-300 ${
              language === 'ar'
                ? 'scale-110 text-white'
                : 'scale-100 text-gray-600 dark:text-gray-400'
            }`}
          >
            ع
          </span>
        </div>

        {/* English side */}
        <div className="relative z-10 flex h-8 w-8 items-center justify-center">
          <span
            className={`text-xs font-bold transition-all duration-300 ${
              language === 'en'
                ? 'scale-110 text-white'
                : 'scale-100 text-gray-600 dark:text-gray-400'
            }`}
          >
            EN
          </span>
        </div>

        {/* Hover effect */}
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400 to-indigo-500 opacity-0 transition-opacity duration-300 group-hover:opacity-10" />
      </button>

      {/* Tooltip */}
      <div className="pointer-events-none absolute -bottom-8 left-1/2 -translate-x-1/2 transform opacity-0 transition-opacity duration-300 group-hover:opacity-100">
        <div className="rounded bg-gray-900 px-2 py-1 text-xs text-white dark:bg-gray-100 dark:text-gray-900">
          {language === 'ar' ? 'Switch to English' : 'التبديل للعربية'}
        </div>
      </div>
    </div>
  )
}
