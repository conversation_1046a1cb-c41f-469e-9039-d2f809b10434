'use client'

import { LightBulbIcon } from '@heroicons/react/24/outline'
import { useTranslation } from '@/hooks/useTranslation'

interface ReflectionPromptProps {
  reflection: string
  className?: string
}

export default function ReflectionPrompt({ reflection, className = '' }: ReflectionPromptProps) {
  const { t } = useTranslation()
  return (
    <div
      className={`rounded-lg border border-amber-200 bg-gradient-to-r from-amber-50 to-yellow-50 p-6 dark:border-amber-700 dark:from-amber-900/20 dark:to-yellow-900/20 ${className}`}
    >
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <LightBulbIcon className="h-6 w-6 text-amber-600 dark:text-amber-400" />
        </div>
        <div className="flex-1">
          <h3 className="mb-3 text-lg font-semibold text-amber-900 dark:text-amber-100">
            {t('verse.reflectionTitle')}
          </h3>
          <p className="leading-relaxed text-amber-800 dark:text-amber-200">{reflection}</p>

          {/* Optional: Add a space for personal notes */}
          <div className="mt-4 rounded-md border border-amber-200 bg-white/50 p-3 dark:border-amber-600 dark:bg-amber-900/10">
            <label
              htmlFor="personal-reflection"
              className="mb-2 block text-sm font-medium text-amber-800 dark:text-amber-200"
            >
              {t('verse.personalNotes')}
            </label>
            <textarea
              id="personal-reflection"
              rows={3}
              className="w-full resize-none border-0 bg-transparent text-sm text-amber-900 placeholder-amber-600 focus:ring-0 dark:text-amber-100 dark:placeholder-amber-400"
              placeholder={t('verse.notesPlaceholder')}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
