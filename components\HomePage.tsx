'use client'

import VerseLayout from '@/components/VerseLayout'
import VerseCard from '@/components/VerseCard'
import Link from '@/components/Link'
import { MDXLayoutRenderer } from 'pliny/mdx-components'
import { components } from '@/components/MDXComponents'
import HomeChallengeSection from '@/components/HomeChallengeSection'
import NewsletterSubscription from '@/components/NewsletterSubscription'
import { useTranslation } from '@/hooks/useTranslation'
import { Verse } from 'contentlayer/generated'

interface HomePageProps {
  todaysVerse: Verse
  recentVerses: Verse[]
}

export default function HomePage({ todaysVerse, recentVerses }: HomePageProps) {
  const { t } = useTranslation()

  if (!todaysVerse) {
    return (
      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        <div className="space-y-2 pt-6 pb-8 md:space-y-5">
          <h1 className="text-3xl leading-9 font-extrabold tracking-tight text-gray-900 sm:text-4xl sm:leading-10 md:text-6xl md:leading-14 dark:text-gray-100">
            {t('home.welcome')}
          </h1>
          <p className="text-lg leading-7 text-gray-500 dark:text-gray-400">{t('home.noVerses')}</p>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Today's Verse Section */}
      <section className="mb-16">
        <div className="mb-8 text-center">
          <h1 className="mb-4 text-4xl font-bold text-gray-900 dark:text-gray-100">
            {t('home.title')}
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400">{t('home.subtitle')}</p>
        </div>

        <div className="mx-auto max-w-4xl">
          <VerseLayout verse={todaysVerse}>
            <MDXLayoutRenderer code={todaysVerse.body.code} components={components} />
          </VerseLayout>
        </div>
      </section>

      {/* Challenge Section */}
      <HomeChallengeSection verseCategory={todaysVerse.tags?.[0]} />

      {/* Newsletter Subscription Section */}
      <section className="mb-16" id="newsletter">
        <NewsletterSubscription />
      </section>

      {/* Recent Verses Section */}
      <section>
        <div className="mb-8 flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {t('home.recentVerses')}
          </h2>
          <Link
            href="/verses"
            className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
          >
            {t('home.viewAllVerses')}
          </Link>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {recentVerses.map((verse) => (
            <VerseCard
              key={verse.slug}
              slug={verse.slug}
              title={verse.title}
              surah={verse.surah}
              ayah={verse.ayah}
              date={verse.date}
              tags={verse.tags}
              lesson={verse.lesson}
              reflection={verse.reflection}
            />
          ))}
        </div>
      </section>
    </>
  )
}
