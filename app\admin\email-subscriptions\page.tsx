'use client'

import { useState, useEffect } from 'react'
import {
  ChartBarIcon,
  EnvelopeIcon,
  UserGroupIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline'

interface SubscriptionStats {
  total: number
  active: number
  daily: number
  weekly: number
}

interface EmailLog {
  id: string
  subscriptionId: string
  verseSlug: string
  sentAt: string
  emailType: 'new_verse' | 'daily_reminder' | 'weekly_reminder'
  success: boolean
  errorMessage?: string
}

export default function EmailSubscriptionsAdmin() {
  const [stats, setStats] = useState<SubscriptionStats | null>(null)
  const [logs, setLogs] = useState<EmailLog[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSending, setIsSending] = useState(false)
  const [testResult, setTestResult] = useState<string | null>(null)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setIsLoading(true)

      // Fetch stats
      const statsResponse = await fetch('/api/email/notify?action=stats')
      const statsData = await statsResponse.json()

      // Fetch logs
      const logsResponse = await fetch('/api/email/notify?action=logs')
      const logsData = await logsResponse.json()

      if (statsResponse.ok) {
        setStats(statsData.stats)
      }

      if (logsResponse.ok) {
        setLogs(logsData.logs)
      }
    } catch (error) {
      console.error('Failed to fetch data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const sendTestEmail = async (type: 'daily_reminder' | 'weekly_reminder') => {
    try {
      setIsSending(true)
      setTestResult(null)

      const response = await fetch('/api/email/notify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.NEXT_PUBLIC_CRON_SECRET || 'your-secret-key'}`,
        },
        body: JSON.stringify({ type }),
      })

      const result = await response.json()

      if (response.ok) {
        setTestResult(
          `✅ ${type} emails sent successfully: ${result.sent} sent, ${result.failed} failed`
        )
        fetchData() // Refresh data
      } else {
        setTestResult(`❌ Failed to send ${type} emails: ${result.error}`)
      }
    } catch (error) {
      setTestResult(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsSending(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  if (isLoading) {
    return (
      <div className="mx-auto max-w-7xl px-4 py-8">
        <div className="animate-pulse">
          <div className="mb-8 h-8 w-64 rounded bg-gray-200 dark:bg-gray-700"></div>
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 rounded-lg bg-gray-200 dark:bg-gray-700"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="mx-auto max-w-7xl px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          إدارة الاشتراكات البريدية
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          إحصائيات وإدارة النشرة البريدية للموقع
        </p>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="mb-8 grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
            <div className="flex items-center">
              <UserGroupIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <div className="mr-4">
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.total}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي المشتركين</p>
              </div>
            </div>
          </div>

          <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
            <div className="flex items-center">
              <CheckCircleIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
              <div className="mr-4">
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {stats.active}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">المشتركون النشطون</p>
              </div>
            </div>
          </div>

          <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
            <div className="flex items-center">
              <ClockIcon className="h-8 w-8 text-orange-600 dark:text-orange-400" />
              <div className="mr-4">
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.daily}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">اشتراك يومي</p>
              </div>
            </div>
          </div>

          <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
            <div className="flex items-center">
              <ChartBarIcon className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              <div className="mr-4">
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {stats.weekly}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">اشتراك أسبوعي</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Test Email Section */}
      <div className="mb-8 rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
        <h2 className="mb-4 text-xl font-semibold text-gray-900 dark:text-gray-100">
          إرسال رسائل تجريبية
        </h2>

        <div className="flex gap-4">
          <button
            onClick={() => sendTestEmail('daily_reminder')}
            disabled={isSending}
            className="flex items-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none disabled:opacity-50 dark:focus:ring-offset-gray-800"
          >
            <EnvelopeIcon className="h-4 w-4" />
            إرسال تذكير يومي
          </button>

          <button
            onClick={() => sendTestEmail('weekly_reminder')}
            disabled={isSending}
            className="flex items-center gap-2 rounded-md bg-purple-600 px-4 py-2 text-white hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:outline-none disabled:opacity-50 dark:focus:ring-offset-gray-800"
          >
            <EnvelopeIcon className="h-4 w-4" />
            إرسال تذكير أسبوعي
          </button>
        </div>

        {testResult && (
          <div className="mt-4 rounded-md bg-gray-50 p-3 dark:bg-gray-700">
            <p className="text-sm text-gray-700 dark:text-gray-300">{testResult}</p>
          </div>
        )}
      </div>

      {/* Email Logs */}
      <div className="rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
        <div className="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            سجل الرسائل الأخيرة
          </h2>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-400">
                  التاريخ
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-400">
                  نوع الرسالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-400">
                  الآية
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-400">
                  الحالة
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
              {logs.map((log) => (
                <tr key={log.id}>
                  <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900 dark:text-gray-100">
                    {formatDate(log.sentAt)}
                  </td>
                  <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900 dark:text-gray-100">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${
                        log.emailType === 'new_verse'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : log.emailType === 'daily_reminder'
                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                            : 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400'
                      }`}
                    >
                      {log.emailType === 'new_verse'
                        ? 'آية جديدة'
                        : log.emailType === 'daily_reminder'
                          ? 'تذكير يومي'
                          : 'تذكير أسبوعي'}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900 dark:text-gray-100">
                    {log.verseSlug}
                  </td>
                  <td className="px-6 py-4 text-sm whitespace-nowrap">
                    {log.success ? (
                      <CheckCircleIcon className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircleIcon className="h-5 w-5 text-red-500" />
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {logs.length === 0 && (
            <div className="px-6 py-8 text-center">
              <p className="text-gray-500 dark:text-gray-400">لا توجد رسائل مرسلة حتى الآن</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
