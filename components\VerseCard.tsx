'use client'

import Link from '@/components/Link'
import Tag from '@/components/Tag'
import { formatDate } from 'pliny/utils/formatDate'
import siteMetadata from '@/data/siteMetadata'
import { useTranslation } from '@/hooks/useTranslation'

interface VerseCardProps {
  slug: string
  title: string
  surah: string
  ayah: number
  date: string
  tags?: string[]
  lesson: string
  reflection: string
}

export default function VerseCard({
  slug,
  title,
  surah,
  ayah,
  date,
  tags = [],
  lesson,
  reflection,
}: VerseCardProps) {
  const { t } = useTranslation()
  return (
    <article className="group relative rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-all hover:shadow-md dark:border-gray-700 dark:bg-gray-800">
      <div className="flex flex-col space-y-4">
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <Link
              href={`/verses/${slug}`}
              className="block text-xl font-bold text-gray-900 transition-colors hover:text-blue-600 dark:text-gray-100 dark:hover:text-blue-400"
            >
              {title}
            </Link>
            <div className="mt-1 flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
              <span>{surah}</span>
              <span>•</span>
              <span>
                {t('verse.verse')} {ayah}
              </span>
              <span>•</span>
              <time dateTime={date}>{formatDate(date, siteMetadata.locale)}</time>
            </div>
          </div>
        </div>

        {/* Tags */}
        {tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <Tag key={tag} text={tag} />
            ))}
          </div>
        )}

        {/* Content Preview */}
        <div className="space-y-3">
          <div>
            <h4 className="mb-1 text-sm font-medium text-gray-800 dark:text-gray-200">
              {t('verse.practicalLesson')}
            </h4>
            <p className="line-clamp-2 text-sm text-gray-600 dark:text-gray-400">{lesson}</p>
          </div>

          <div>
            <h4 className="mb-1 text-sm font-medium text-gray-800 dark:text-gray-200">
              {t('verse.reflection')}
            </h4>
            <p className="line-clamp-2 text-sm text-gray-600 dark:text-gray-400">{reflection}</p>
          </div>
        </div>

        {/* Read More Link */}
        <div className="pt-2">
          <Link
            href={`/verses/${slug}`}
            className="inline-flex items-center text-sm font-medium text-blue-600 transition-colors hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
            aria-label={`${t('verse.readMoreAbout')} ${title}`}
          >
            {t('verse.readMore')}
            <svg
              className="mr-1 h-4 w-4 transition-transform group-hover:translate-x-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </Link>
        </div>
      </div>
    </article>
  )
}
