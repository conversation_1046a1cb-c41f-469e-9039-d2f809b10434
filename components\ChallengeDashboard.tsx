'use client'

import { useState } from 'react'
import { useChallengeProgress } from '@/hooks/useProgress'
import { getChallengeById, getChallengeDayData } from '@/data/challenges'
import ChallengeProgress from './ChallengeProgress'
import ChallengeEnrollment from './ChallengeEnrollment'
import ChallengeStats from './ChallengeStats'
import ApplyToday from './ApplyToday'
import { CalendarIcon, BookOpenIcon } from '@heroicons/react/24/outline'
import Link from '@/components/Link'

export default function ChallengeDashboard() {
  const { currentChallenge, getChallengeProgress, getChallengeHistory } = useChallengeProgress()
  const [showHistory, setShowHistory] = useState(false)

  const progress = getChallengeProgress()
  const history = getChallengeHistory()
  const challenge = currentChallenge ? getChallengeById(currentChallenge.id) : null

  // Get current day data for active challenge
  const currentDayData =
    challenge && progress?.isActive ? getChallengeDayData(challenge.id, progress.currentDay) : null

  return (
    <div className="space-y-8">
      {/* Current Challenge Section */}
      {currentChallenge?.isActive && challenge && progress ? (
        <div className="space-y-6">
          <ChallengeProgress challengeId={challenge.id} />

          {/* Current Day Task */}
          {currentDayData && (
            <div>
              <h2 className="mb-4 text-xl font-semibold text-gray-900 dark:text-gray-100">
                مهمة اليوم
              </h2>
              <ApplyToday
                slug={`challenge-${challenge.id}-day-${progress.currentDay}`}
                lesson={currentDayData.task}
                reflection={currentDayData.reflection}
                challengeMode={true}
                challengeId={challenge.id}
                challengeDay={progress.currentDay}
              />
            </div>
          )}

          {/* Related Verse Link */}
          {currentDayData?.verse && (
            <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-800">
              <div className="flex items-center">
                <BookOpenIcon className="ml-2 h-5 w-5 text-gray-600 dark:text-gray-400" />
                <span className="text-sm text-gray-600 dark:text-gray-400">الآية المرتبطة:</span>
                <Link
                  href={`/verses/${currentDayData.verse}`}
                  className="mr-2 text-sm font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  اقرأ الآية
                </Link>
              </div>
            </div>
          )}
        </div>
      ) : (
        /* Challenge Enrollment */
        <ChallengeEnrollment />
      )}

      {/* Statistics */}
      <ChallengeStats />

      {/* Challenge History */}
      {history.length > 0 && (
        <div>
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">سجل التحديات</h2>
            <button
              onClick={() => setShowHistory(!showHistory)}
              className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
            >
              {showHistory ? 'إخفاء' : 'عرض الكل'}
            </button>
          </div>

          {showHistory && (
            <div className="space-y-3">
              {history
                .slice()
                .reverse()
                .map((challengeRecord, index) => {
                  const challengeInfo = getChallengeById(challengeRecord.id)
                  if (!challengeInfo) return null

                  return (
                    <div
                      key={`${challengeRecord.id}-${index}`}
                      className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <span className="text-lg">{challengeInfo.icon}</span>
                          <div className="mr-3">
                            <h3 className="font-medium text-gray-900 dark:text-gray-100">
                              {challengeInfo.title}
                            </h3>
                            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                              <CalendarIcon className="ml-1 h-3 w-3" />
                              <span>
                                {new Date(challengeRecord.startDate).toLocaleDateString('ar-SA')}
                              </span>
                              {challengeRecord.completedDate && (
                                <>
                                  <span className="mx-2">-</span>
                                  <span>
                                    {new Date(challengeRecord.completedDate).toLocaleDateString(
                                      'ar-SA'
                                    )}
                                  </span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="text-left">
                          <div
                            className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                              challengeRecord.completedDate
                                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                                : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                            }`}
                          >
                            {challengeRecord.completedDate ? 'مكتمل' : 'غير مكتمل'}
                          </div>
                          <div className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                            {challengeRecord.completedDays.length}/7 أيام
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
