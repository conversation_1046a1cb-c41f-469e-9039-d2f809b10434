'use client'

import { useChallengeProgress } from '@/hooks/useProgress'
import { FireIcon, TrophyIcon, CalendarIcon, CheckIcon } from '@heroicons/react/24/outline'

export default function ChallengeStats() {
  const { getChallengeStats, getChallengeHistory } = useChallengeProgress()

  const stats = getChallengeStats()
  const history = getChallengeHistory()

  if (stats.totalChallenges === 0) {
    return null
  }

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
      <h3 className="mb-4 flex items-center text-lg font-semibold text-gray-900 dark:text-gray-100">
        <TrophyIcon className="ml-2 h-5 w-5 text-yellow-600 dark:text-yellow-400" />
        إحصائياتي
      </h3>

      <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
        {/* Total Challenges */}
        <div className="text-center">
          <div className="mx-auto mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30">
            <CalendarIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {stats.totalChallenges}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">تحديات</div>
        </div>

        {/* Completed Challenges */}
        <div className="text-center">
          <div className="mx-auto mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30">
            <CheckIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {stats.completedChallenges}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">مكتملة</div>
        </div>

        {/* Current Streak */}
        <div className="text-center">
          <div className="mx-auto mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/30">
            <FireIcon className="h-6 w-6 text-orange-600 dark:text-orange-400" />
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {stats.currentStreak}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">متتالية</div>
        </div>

        {/* Total Days */}
        <div className="text-center">
          <div className="mx-auto mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/30">
            <TrophyIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {stats.totalDaysCompleted}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">أيام</div>
        </div>
      </div>

      {/* Completion Rate */}
      {stats.totalChallenges > 0 && (
        <div className="mt-6">
          <div className="mb-2 flex justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">معدل الإنجاز</span>
            <span className="font-medium text-gray-900 dark:text-gray-100">
              {Math.round((stats.completedChallenges / stats.totalChallenges) * 100)}%
            </span>
          </div>
          <div className="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700">
            <div
              className="h-2 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 transition-all duration-300"
              style={{
                width: `${(stats.completedChallenges / stats.totalChallenges) * 100}%`,
              }}
            />
          </div>
        </div>
      )}

      {/* Recent Achievements */}
      {stats.currentStreak >= 2 && (
        <div className="mt-4 rounded-lg bg-orange-50 p-3 dark:bg-orange-900/20">
          <div className="flex items-center">
            <FireIcon className="ml-2 h-4 w-4 text-orange-600 dark:text-orange-400" />
            <span className="text-sm font-medium text-orange-800 dark:text-orange-400">
              رائع! لديك {stats.currentStreak} تحديات متتالية مكتملة!
            </span>
          </div>
        </div>
      )}
    </div>
  )
}
