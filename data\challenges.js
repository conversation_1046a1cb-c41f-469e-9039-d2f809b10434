/**
 * 7-Day Quranic Values Challenge System
 * Each challenge focuses on applying Quranic values through daily practical tasks
 */

export const challenges = {
  'gratitude-path': {
    id: 'gratitude-path',
    title: 'مسار الشكر والحمد',
    description: 'رحلة 7 أيام لتطبيق قيم الشكر والحمد في الحياة اليومية',
    duration: 7,
    category: 'حمد',
    color: 'emerald',
    icon: '🤲',
    days: [
      {
        day: 1,
        title: 'بداية الشكر',
        verse: 'al-fatiha-2',
        task: 'اكتب 5 نعم تحمد الله عليها كل صباح',
        reflection: 'كيف غيّر الشكر نظرتك لليوم؟',
        criteria: 'كتابة قائمة الشكر الصباحية',
        estimatedTime: '10 دقائق',
      },
      {
        day: 2,
        title: 'شكر النعم الصغيرة',
        verse: 'al-fatiha-2',
        task: 'احمد الله على 3 أشياء صغيرة لم تلاحظها من قبل',
        reflection: 'ما النعم الخفية التي اكتشفتها اليوم؟',
        criteria: 'تحديد وشكر النعم الصغيرة',
        estimatedTime: '15 دقيقة',
      },
      {
        day: 3,
        title: 'شكر في الصعوبات',
        verse: 'al-fatiha-2',
        task: 'ابحث عن حكمة أو خير في تحدٍ تواجهه واحمد الله عليه',
        reflection: 'كيف يمكن للشكر أن يحول التحديات إلى نعم؟',
        criteria: 'إيجاد الخير في التحديات',
        estimatedTime: '20 دقيقة',
      },
      {
        day: 4,
        title: 'شكر الآخرين',
        verse: 'al-fatiha-2',
        task: 'اشكر 3 أشخاص أثروا في حياتك واحمد الله على وجودهم',
        reflection: 'كيف أثر شكر الآخرين على علاقاتك؟',
        criteria: 'شكر الأشخاص المؤثرين',
        estimatedTime: '25 دقيقة',
      },
      {
        day: 5,
        title: 'شكر القدرات',
        verse: 'al-fatiha-2',
        task: 'احمد الله على قدراتك ومواهبك واستخدمها في خدمة الآخرين',
        reflection: 'كيف يمكنني استخدام مواهبي لشكر الله؟',
        criteria: 'استخدام المواهب في الخير',
        estimatedTime: '30 دقيقة',
      },
      {
        day: 6,
        title: 'شكر الذكريات',
        verse: 'al-fatiha-2',
        task: 'تذكر 5 ذكريات جميلة واحمد الله على كل منها',
        reflection: 'كيف تؤثر الذكريات الجميلة على حالتك النفسية؟',
        criteria: 'استحضار وشكر الذكريات الجميلة',
        estimatedTime: '20 دقيقة',
      },
      {
        day: 7,
        title: 'شكر المستقبل',
        verse: 'al-fatiha-2',
        task: 'احمد الله مسبقاً على الخير القادم وضع خطة لاستمرار الشكر',
        reflection: 'كيف سأحافظ على عادة الشكر في حياتي؟',
        criteria: 'وضع خطة لاستمرار الشكر',
        estimatedTime: '35 دقيقة',
      },
    ],
  },
  'trust-path': {
    id: 'trust-path',
    title: 'مسار التوكل على الله',
    description: 'رحلة 7 أيام لتقوية التوكل والثقة بالله في جميع أمور الحياة',
    duration: 7,
    category: 'توكل',
    color: 'blue',
    icon: '🤲',
    days: [
      {
        day: 1,
        title: 'فهم التوكل',
        verse: 'al-baqara-255',
        task: 'تأمل في معنى التوكل واكتب تعريفك الشخصي له',
        reflection: 'ما الفرق بين التوكل والتواكل؟',
        criteria: 'كتابة تعريف شخصي للتوكل',
        estimatedTime: '15 دقيقة',
      },
      {
        day: 2,
        title: 'التوكل في القرارات',
        verse: 'al-baqara-255',
        task: 'اتخذ قراراً مهماً بعد الاستخارة والتوكل على الله',
        reflection: 'كيف أثر التوكل على ثقتي في القرار؟',
        criteria: 'اتخاذ قرار بالاستخارة والتوكل',
        estimatedTime: '30 دقيقة',
      },
      {
        day: 3,
        title: 'التوكل في العمل',
        verse: 'al-baqara-255',
        task: 'ابذل جهدك في مهمة مهمة ثم توكل على الله في النتائج',
        reflection: 'كيف يحررني التوكل من القلق حول النتائج؟',
        criteria: 'بذل الجهد مع التوكل في النتائج',
        estimatedTime: '45 دقيقة',
      },
      {
        day: 4,
        title: 'التوكل في الرزق',
        verse: 'al-baqara-255',
        task: 'اعمل بجد لكسب الرزق مع اليقين أن الله هو الرازق',
        reflection: 'كيف يؤثر التوكل على نظرتي للرزق؟',
        criteria: 'العمل مع اليقين بأن الله هو الرازق',
        estimatedTime: '40 دقيقة',
      },
      {
        day: 5,
        title: 'التوكل في الصحة',
        verse: 'al-baqara-255',
        task: 'اتبع أسباب الصحة وتوكل على الله في الشفاء والعافية',
        reflection: 'كيف يساعدني التوكل في التعامل مع المرض؟',
        criteria: 'اتباع أسباب الصحة مع التوكل',
        estimatedTime: '25 دقيقة',
      },
      {
        day: 6,
        title: 'التوكل في العلاقات',
        verse: 'al-baqara-255',
        task: 'ابذل جهدك في إصلاح علاقة مهمة وتوكل على الله',
        reflection: 'كيف يحسن التوكل من علاقاتي؟',
        criteria: 'العمل على إصلاح العلاقات مع التوكل',
        estimatedTime: '35 دقيقة',
      },
      {
        day: 7,
        title: 'التوكل كأسلوب حياة',
        verse: 'al-baqara-255',
        task: 'ضع خطة لجعل التوكل جزءاً من روتينك اليومي',
        reflection: 'كيف سأحافظ على التوكل في حياتي؟',
        criteria: 'وضع خطة لاستمرار التوكل',
        estimatedTime: '30 دقيقة',
      },
    ],
  },
}

export const challengeCategories = {
  حمد: {
    name: 'الحمد والشكر',
    description: 'تحديات تركز على تطبيق قيم الحمد والشكر',
    color: 'emerald',
    challenges: ['gratitude-path'],
  },
  توكل: {
    name: 'التوكل والثقة',
    description: 'تحديات تركز على تقوية التوكل على الله',
    color: 'blue',
    challenges: ['trust-path'],
  },
}

/**
 * Get challenge by ID
 */
export function getChallengeById(challengeId) {
  return challenges[challengeId] || null
}

/**
 * Get all available challenges
 */
export function getAllChallenges() {
  return Object.values(challenges)
}

/**
 * Get challenges by category
 */
export function getChallengesByCategory(category) {
  return Object.values(challenges).filter((challenge) => challenge.category === category)
}

/**
 * Get challenge day data
 */
export function getChallengeDayData(challengeId, dayNumber) {
  const challenge = getChallengeById(challengeId)
  if (!challenge || dayNumber < 1 || dayNumber > challenge.duration) {
    return null
  }
  return challenge.days[dayNumber - 1]
}
