import Database from 'better-sqlite3'
import path from 'path'

// Path to the database file
const dbPath =
  process.env.NODE_ENV === 'production'
    ? '/tmp/subscriptions.db'
    : path.join(process.cwd(), 'data', 'subscriptions.db')

console.log('Cleaning database at:', dbPath)

// Connect to the database
const db = new Database(dbPath)

// Clear all data from the subscriptions table
console.log('Clearing subscriptions table...')
db.exec('DELETE FROM subscriptions')

// Clear all data from the email logs table
console.log('Clearing email logs table...')
db.exec('DELETE FROM email_logs')

console.log('Database cleaned successfully!')
db.close()
