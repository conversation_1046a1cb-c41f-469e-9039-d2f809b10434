import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import ApplyToday from '@/components/ApplyToday'

// Mock the useProgress hook
jest.mock('@/hooks/useProgress', () => ({
  useProgress: jest.fn(),
}))

import { useProgress } from '@/hooks/useProgress'

describe('ApplyToday Component', () => {
  const mockProps = {
    slug: 'test-verse',
    lesson: 'خطوة عملية اليوم: خصص 5 دقائق للتفكر في نعم الله.',
    reflection: 'سؤال: ما هي النعم التي أنساها والتي تدل على قدرة الله؟',
  }

  const mockUseProgress = {
    isDone: false,
    isLoaded: true,
    toggleDone: jest.fn(),
  }

  beforeEach(() => {
    useProgress.mockReturnValue(mockUseProgress)
    jest.clearAllMocks()
  })

  it('renders loading state when not loaded', () => {
    useProgress.mockReturnValue({
      ...mockUseProgress,
      isLoaded: false,
    })

    render(<ApplyToday {...mockProps} />)

    expect(screen.getByRole('generic')).toHaveClass('animate-pulse')
  })

  it('renders lesson and reflection content', () => {
    render(<ApplyToday {...mockProps} />)

    expect(screen.getByText('تطبيق اليوم')).toBeInTheDocument()
    expect(screen.getByText('الدرس العملي:')).toBeInTheDocument()
    expect(screen.getByText(mockProps.lesson)).toBeInTheDocument()
    expect(screen.getByText('للتفكر:')).toBeInTheDocument()
    expect(screen.getByText(mockProps.reflection)).toBeInTheDocument()
  })

  it('shows "تم الإنجاز" button when not done', () => {
    render(<ApplyToday {...mockProps} />)

    expect(screen.getByText('هل طبقت الدرس اليوم؟')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'تم الإنجاز' })).toBeInTheDocument()
  })

  it('shows "إلغاء الإنجاز" button when done', () => {
    useProgress.mockReturnValue({
      ...mockUseProgress,
      isDone: true,
    })

    render(<ApplyToday {...mockProps} />)

    expect(screen.getByText('تم الإنجاز!')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'إلغاء الإنجاز' })).toBeInTheDocument()
  })

  it('calls toggleDone when button is clicked', async () => {
    render(<ApplyToday {...mockProps} />)

    const button = screen.getByRole('button', { name: 'تم الإنجاز' })
    fireEvent.click(button)

    expect(mockUseProgress.toggleDone).toHaveBeenCalledTimes(1)
  })

  it('has proper RTL styling', () => {
    render(<ApplyToday {...mockProps} />)

    const container = screen.getByText('تطبيق اليوم').closest('div')
    expect(container).toHaveClass('rounded-lg', 'border', 'p-6')
  })

  it('displays correct Arabic text direction', () => {
    render(<ApplyToday {...mockProps} />)

    const lessonText = screen.getByText(mockProps.lesson)
    const reflectionText = screen.getByText(mockProps.reflection)

    expect(lessonText).toBeInTheDocument()
    expect(reflectionText).toBeInTheDocument()

    // Check that Arabic text is properly displayed
    expect(lessonText.textContent).toContain('خطوة عملية')
    expect(reflectionText.textContent).toContain('سؤال:')
  })

  it('has accessible button labels', () => {
    render(<ApplyToday {...mockProps} />)

    const button = screen.getByRole('button')
    expect(button).toHaveAttribute('aria-label', 'تم الإنجاز')
  })

  it('changes button state correctly when toggled', async () => {
    const { rerender } = render(<ApplyToday {...mockProps} />)

    // Initially not done
    expect(screen.getByRole('button', { name: 'تم الإنجاز' })).toBeInTheDocument()

    // Simulate state change to done
    useProgress.mockReturnValue({
      ...mockUseProgress,
      isDone: true,
    })

    rerender(<ApplyToday {...mockProps} />)

    expect(screen.getByRole('button', { name: 'إلغاء الإنجاز' })).toBeInTheDocument()
    expect(screen.getByText('تم الإنجاز!')).toBeInTheDocument()
  })
})
