import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { getVerseBySlug, getAllVerses } from '@/lib/verses'
import { generateVerseMetadata } from '@/lib/metadata'
import VerseLayout from '@/components/VerseLayout'
import { MDXLayoutRenderer } from 'pliny/mdx-components'
import { components } from '@/components/MDXComponents'

interface PageProps {
  params: Promise<{
    slug: string
  }>
}

export async function generateStaticParams() {
  const verses = getAllVerses()
  return verses.map((verse) => ({
    slug: verse.slug,
  }))
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug } = await params
  const verse = getVerseBySlug(slug)

  if (!verse) {
    return {
      title: 'الآية غير موجودة',
    }
  }

  return generateVerseMetadata(verse)
}

export default async function VersePage({ params }: PageProps) {
  const { slug } = await params
  const verse = getVerseBySlug(slug)

  if (!verse) {
    notFound()
  }

  return (
    <div className="mx-auto max-w-4xl px-4 py-8">
      <VerseLayout verse={verse}>
        <MDXLayoutRenderer code={verse.body.code} components={components} />
      </VerseLayout>
    </div>
  )
}
