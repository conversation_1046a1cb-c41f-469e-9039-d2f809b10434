import { MetadataRoute } from 'next'
import { allVerses } from 'contentlayer/generated'
import siteMetadata from '@/data/siteMetadata'

export const dynamic = 'force-static'

export default function sitemap(): MetadataRoute.Sitemap {
  const siteUrl = siteMetadata.siteUrl

  const verseRoutes = allVerses
    .filter((verse) => !verse.draft)
    .map((verse) => ({
      url: `${siteUrl}/${verse.path}`,
      lastModified: verse.lastmod || verse.date,
    }))

  const routes = ['', 'blog', 'projects', 'tags'].map((route) => ({
    url: `${siteUrl}/${route}`,
    lastModified: new Date().toISOString().split('T')[0],
  }))

  return [...routes, ...verseRoutes]
}
