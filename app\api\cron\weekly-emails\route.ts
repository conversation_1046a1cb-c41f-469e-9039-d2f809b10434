import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Verify the request is from Vercel Cron or authorized source
    const authHeader = request.headers.get('authorization')
    const expectedAuth =
      process.env.CRON_SECRET || process.env.NEXT_PUBLIC_CRON_SECRET || 'your-secret-key'

    if (authHeader !== `Bear<PERSON> ${expectedAuth}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Call the email notification API to send weekly reminders
    const baseUrl = process.env.VERCEL_URL
      ? `https://${process.env.VERCEL_URL}`
      : 'http://localhost:3000'

    const response = await fetch(`${baseUrl}/api/email/notify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${expectedAuth}`,
      },
      body: JSON.stringify({
        type: 'weekly_reminder',
      }),
    })

    const result = await response.json()

    if (response.ok) {
      return NextResponse.json({
        success: true,
        message: 'Weekly emails sent successfully',
        ...result,
      })
    } else {
      return NextResponse.json(
        { error: 'Failed to send weekly emails', details: result },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Weekly email cron error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
