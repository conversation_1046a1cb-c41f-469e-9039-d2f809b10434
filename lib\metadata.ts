import { Metadata } from 'next'
import siteMetadata from '@/data/siteMetadata'

interface VerseMetadata {
  title: string
  surah: string
  ayah: number
  slug: string
  date: string
  lesson: string
  ogImage?: string
  lastmod?: string
}

export function generateVerseMetadata(verse: VerseMetadata): Metadata {
  const { title, surah, ayah, lesson, ogImage, date, lastmod } = verse
  const description = `${surah} آية ${ayah} - ${lesson}`
  const publishedAt = new Date(date).toISOString()
  const modifiedAt = new Date(lastmod || date).toISOString()
  const imageUrl = ogImage || siteMetadata.socialBanner

  return {
    title,
    description,
    openGraph: {
      title: `${title} | ${siteMetadata.title}`,
      description,
      siteName: siteMetadata.title,
      locale: 'ar_SA',
      type: 'article',
      publishedTime: publishedAt,
      modifiedTime: modifiedAt,
      url: `${siteMetadata.siteUrl}/verses/${verse.slug}`,
      images: [
        {
          url: imageUrl,
          alt: title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: `${title} | ${siteMetadata.title}`,
      description,
      images: [imageUrl],
    },
  }
}
