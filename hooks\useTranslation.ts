import { useLanguage } from '@/contexts/LanguageContext'
import { translations } from '@/data/translations'

export function useTranslation() {
  const { language } = useLanguage()

  const t = (key: string): string => {
    const keys = key.split('.')
    let value: any = translations[language]

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        // Fallback to Arabic if key not found in current language
        value = translations.ar
        for (const fallbackKey of keys) {
          if (value && typeof value === 'object' && fallbackKey in value) {
            value = value[fallbackKey]
          } else {
            return key // Return key if not found in fallback either
          }
        }
        break
      }
    }

    return typeof value === 'string' ? value : key
  }

  return { t, language }
}
