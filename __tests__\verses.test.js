import {
  getAllVerses,
  getVerseBySlug,
  getTodaysVerse,
  getVersesByTag,
  getAllTags,
  getVersesBySurah,
  getRandomVerse,
  searchVerses,
} from '@/lib/verses'

// Mock contentlayer generated data
jest.mock('contentlayer/generated', () => ({
  allVerses: [
    {
      slug: 'al-baqara-255',
      title: 'آية الكرسي',
      surah: 'البقرة',
      ayah: 255,
      date: '2025-08-21',
      tags: ['توكل', 'إيمان'],
      lesson: 'خطوة عملية اليوم: خصص 5 دقائق للتفكر في نعم الله.',
      reflection: 'سؤال: ما هي النعم التي أنساها؟',
      draft: false,
    },
    {
      slug: 'al-fatiha-2',
      title: 'الحمد لله رب العالمين',
      surah: 'الفاتحة',
      ayah: 2,
      date: '2025-08-22',
      tags: ['حمد', 'شكر'],
      lesson: 'خطوة عملية اليوم: اكتب 3 أشياء تحمد الله عليها.',
      reflection: 'سؤال: كيف يمكنني أن أجعل الحمد جزءاً من روتيني؟',
      draft: false,
    },
    {
      slug: 'draft-verse',
      title: 'آية مسودة',
      surah: 'آل عمران',
      ayah: 1,
      date: '2025-08-23',
      tags: ['test'],
      lesson: 'درس تجريبي',
      reflection: 'تفكر تجريبي',
      draft: true,
    },
  ],
}))

// Mock pliny utils
jest.mock('pliny/utils/contentlayer', () => ({
  sortPosts: jest.fn((posts) => posts.sort((a, b) => new Date(b.date) - new Date(a.date))),
}))

describe('Verses Library Functions', () => {
  describe('getAllVerses', () => {
    it('should return all published verses sorted by date', () => {
      const verses = getAllVerses()

      expect(verses).toHaveLength(2) // Should exclude draft
      expect(verses[0].slug).toBe('al-fatiha-2') // Most recent first
      expect(verses[1].slug).toBe('al-baqara-255')
    })

    it('should exclude draft verses', () => {
      const verses = getAllVerses()

      expect(verses.find((v) => v.draft === true)).toBeUndefined()
    })
  })

  describe('getVerseBySlug', () => {
    it('should return verse with matching slug', () => {
      const verse = getVerseBySlug('al-baqara-255')

      expect(verse).toBeDefined()
      expect(verse.slug).toBe('al-baqara-255')
      expect(verse.title).toBe('آية الكرسي')
    })

    it('should return null for non-existent slug', () => {
      const verse = getVerseBySlug('non-existent')

      expect(verse).toBeNull()
    })

    it('should not return draft verses', () => {
      const verse = getVerseBySlug('draft-verse')

      expect(verse).toBeNull()
    })
  })

  describe('getTodaysVerse', () => {
    it('should return a verse based on current date', () => {
      const verse = getTodaysVerse()

      expect(verse).toBeDefined()
      expect(['al-baqara-255', 'al-fatiha-2']).toContain(verse.slug)
    })

    it('should return null when no verses available', () => {
      // Mock empty verses array
      jest.doMock('contentlayer/generated', () => ({
        allVerses: [],
      }))

      // Re-import to get mocked version
      const { getTodaysVerse: getTodaysVerseEmpty } = require('@/lib/verses')
      const verse = getTodaysVerseEmpty()

      expect(verse).toBeNull()
    })
  })

  describe('getVersesByTag', () => {
    it('should return verses with matching tag', () => {
      const verses = getVersesByTag('إيمان')

      expect(verses).toHaveLength(1)
      expect(verses[0].slug).toBe('al-baqara-255')
    })

    it('should be case insensitive', () => {
      const verses = getVersesByTag('إيمان')

      expect(verses).toHaveLength(1)
    })

    it('should return empty array for non-existent tag', () => {
      const verses = getVersesByTag('غير موجود')

      expect(verses).toHaveLength(0)
    })
  })

  describe('getAllTags', () => {
    it('should return all unique tags sorted', () => {
      const tags = getAllTags()

      expect(tags).toContain('توكل')
      expect(tags).toContain('إيمان')
      expect(tags).toContain('حمد')
      expect(tags).toContain('شكر')
      expect(tags).toHaveLength(4)
    })

    it('should not include tags from draft verses', () => {
      const tags = getAllTags()

      expect(tags).not.toContain('test')
    })
  })

  describe('getVersesBySurah', () => {
    it('should return verses from specific surah', () => {
      const verses = getVersesBySurah('البقرة')

      expect(verses).toHaveLength(1)
      expect(verses[0].slug).toBe('al-baqara-255')
    })

    it('should be case insensitive', () => {
      const verses = getVersesBySurah('البقرة')

      expect(verses).toHaveLength(1)
    })
  })

  describe('getRandomVerse', () => {
    it('should return a random verse', () => {
      const verse = getRandomVerse()

      expect(verse).toBeDefined()
      expect(['al-baqara-255', 'al-fatiha-2']).toContain(verse.slug)
    })
  })

  describe('searchVerses', () => {
    it('should search in title', () => {
      const verses = searchVerses('الكرسي')

      expect(verses).toHaveLength(1)
      expect(verses[0].slug).toBe('al-baqara-255')
    })

    it('should search in surah', () => {
      const verses = searchVerses('البقرة')

      expect(verses).toHaveLength(1)
      expect(verses[0].slug).toBe('al-baqara-255')
    })

    it('should search in lesson', () => {
      const verses = searchVerses('تفكر')

      expect(verses).toHaveLength(1)
      expect(verses[0].slug).toBe('al-baqara-255')
    })

    it('should search in tags', () => {
      const verses = searchVerses('حمد')

      expect(verses).toHaveLength(1)
      expect(verses[0].slug).toBe('al-fatiha-2')
    })

    it('should be case insensitive', () => {
      const verses = searchVerses('الكرسي')

      expect(verses).toHaveLength(1)
    })

    it('should return empty array for no matches', () => {
      const verses = searchVerses('غير موجود')

      expect(verses).toHaveLength(0)
    })
  })
})
