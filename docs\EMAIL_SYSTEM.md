# Email Notification System

This document describes the email notification system implemented for the Quran verses explanation website.

## Features

- **Email Subscription**: Simple email subscription form without requiring full account registration
- **Dual Language Support**: Arabic and English email templates and interface
- **Flexible Frequency**: Daily or weekly notification preferences
- **Automated Notifications**: Cron-based daily and weekly reminders
- **New Verse Notifications**: Webhook-triggered emails for new content
- **Subscription Management**: User-friendly preference management and unsubscribe options
- **Admin Dashboard**: Statistics and email log monitoring
- **Email Confirmation**: Double opt-in for subscription confirmation

## Architecture

### Database Layer

- **SQLite Database**: Lightweight database for storing subscriptions and email logs
- **SubscriptionManager**: Handles all subscription CRUD operations
- **EmailLogger**: Tracks email sending history and success/failure rates

### Email Service

- **EmailService**: Handles email composition and sending
- **Responsive Templates**: Mobile-friendly HTML email templates
- **Arabic Text Support**: Proper RTL text rendering in emails
- **Unsubscribe Links**: One-click unsubscribe functionality

### API Routes

- `/api/newsletter` - Subscription management (subscribe, update preferences, get preferences)
- `/api/email/confirm` - Email confirmation handling
- `/api/email/unsubscribe` - Unsubscribe handling
- `/api/email/notify` - Email notification sending (for cron jobs)
- `/api/cron/daily-emails` - Daily email cron job
- `/api/cron/weekly-emails` - Weekly email cron job
- `/api/webhook/new-verse` - New verse notification webhook

## Setup Instructions

### 1. Environment Variables

Copy `.env.example` to `.env.local` and configure:

```env
# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Security
CRON_SECRET=your-secure-random-string
WEBHOOK_SECRET=your-webhook-secret

# Contact
NEXT_PUBLIC_CONTACT_EMAIL=<EMAIL>
```

### 2. Gmail Setup (Recommended)

1. Enable 2-factor authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. Use the generated password as `SMTP_PASS`

### 3. Database Initialization

The database is automatically created when the application starts. No manual setup required.

### 4. Cron Jobs (Vercel)

The system includes pre-configured cron jobs in `vercel.json`:

- **Daily emails**: Every day at 8:00 AM UTC (`0 8 * * *`)
- **Weekly emails**: Every Sunday at 8:00 AM UTC (`0 8 * * 0`)

### 5. Testing

Test the system using the admin dashboard at `/admin/email-subscriptions` or API endpoints:

```bash
# Test daily reminder
curl -X POST https://your-domain.com/api/email/notify \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-cron-secret" \
  -d '{"type": "daily_reminder"}'

# Test weekly reminder
curl -X POST https://your-domain.com/api/email/notify \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-cron-secret" \
  -d '{"type": "weekly_reminder"}'

# Test new verse notification
curl -X POST https://your-domain.com/api/email/notify \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-cron-secret" \
  -d '{"type": "new_verse", "verseSlug": "your-verse-slug"}'
```

## Usage

### For Users

1. **Subscribe**: Use the subscription form on the homepage
2. **Confirm**: Check email and click confirmation link
3. **Manage**: Visit `/email/preferences` to update settings
4. **Unsubscribe**: Click unsubscribe link in any email or use preferences page

### For Administrators

1. **Monitor**: Visit `/admin/email-subscriptions` for statistics and logs
2. **Test**: Send test emails using the admin dashboard
3. **New Verse**: Trigger new verse notifications via webhook

### For Developers

#### Triggering New Verse Notifications

When a new verse is published, trigger notifications:

```javascript
// In your content management system or deployment hook
await fetch('/api/webhook/new-verse', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${process.env.WEBHOOK_SECRET}`,
  },
  body: JSON.stringify({
    verseSlug: 'new-verse-slug',
  }),
})
```

#### Custom Email Templates

Email templates are defined in `lib/email-service.ts`. Customize the HTML templates for your branding:

- `getConfirmationHtmlAr()` - Arabic confirmation email
- `getConfirmationHtmlEn()` - English confirmation email
- `getVerseHtmlAr()` - Arabic verse notification
- `getVerseHtmlEn()` - English verse notification

## Email Templates

### Confirmation Email

- **Subject**: "تأكيد الاشتراك - آيات يومية" / "Confirm Your Subscription - Daily Verses"
- **Content**: Welcome message with confirmation button
- **Action**: Confirms subscription and activates email delivery

### Verse Notification Email

- **Subject**: Includes verse title and notification type
- **Content**:
  - Verse title and reference (Surah and Ayah number)
  - Practical lesson section
  - Reflection question section
  - "Read More" button linking to full article
  - Unsubscribe link
- **Responsive**: Mobile-friendly design with proper Arabic text support

## Security Considerations

1. **API Authentication**: All cron and webhook endpoints require Bearer token authentication
2. **Email Validation**: Server-side email validation and sanitization
3. **Rate Limiting**: Built-in delays between email sends to prevent overwhelming SMTP servers
4. **Secure Tokens**: UUID-based unsubscribe and confirmation tokens
5. **Double Opt-in**: Email confirmation required before activation

## Monitoring and Analytics

### Subscription Statistics

- Total confirmed subscriptions
- Active vs inactive subscriptions
- Daily vs weekly preference breakdown

### Email Logs

- Delivery success/failure rates
- Email type tracking (new verse, daily reminder, weekly reminder)
- Error message logging for failed deliveries
- Timestamp tracking for all email activities

## Troubleshooting

### Common Issues

1. **Emails not sending**

   - Check SMTP credentials
   - Verify Gmail app password
   - Check server logs for authentication errors

2. **Emails going to spam**

   - Set up SPF, DKIM, and DMARC records
   - Use a dedicated sending domain
   - Monitor sender reputation

3. **Database errors**

   - Ensure write permissions for data directory
   - Check disk space on server
   - Verify SQLite installation

4. **Cron jobs not running**
   - Verify Vercel cron configuration
   - Check CRON_SECRET environment variable
   - Monitor Vercel function logs

### Debug Mode

Enable debug logging by setting `NODE_ENV=development` to see detailed email sending logs.

## Future Enhancements

- Email template customization interface
- A/B testing for email content
- Advanced segmentation options
- Integration with email marketing platforms
- Bounce and complaint handling
- Email analytics dashboard
- Automated re-engagement campaigns
