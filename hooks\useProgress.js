'use client'

import { useState, useEffect } from 'react'

/**
 * Custom hook for managing verse completion progress and challenge tracking in localStorage
 * @param {string} slug - The verse slug to track progress for
 * @returns {Object} Object containing isDone state, control functions, and challenge methods
 */
export function useProgress(slug) {
  const [isDone, setIsDone] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  const key = `verse_done_${slug}`

  // Initialize state from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(key)
        setIsDone(stored === 'true')
      } catch (error) {
        console.warn('Failed to read from localStorage:', error)
        setIsDone(false)
      } finally {
        setIsLoaded(true)
      }
    }
  }, [key])

  // Mark verse as done
  const markDone = () => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(key, 'true')
        setIsDone(true)
      } catch (error) {
        console.warn('Failed to write to localStorage:', error)
      }
    }
  }

  // Mark verse as not done
  const markUndone = () => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(key, 'false')
        setIsDone(false)
      } catch (error) {
        console.warn('Failed to write to localStorage:', error)
      }
    }
  }

  // Toggle done state
  const toggleDone = () => {
    if (isDone) {
      markUndone()
    } else {
      markDone()
    }
  }

  // Reset progress (remove from localStorage)
  const reset = () => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem(key)
        setIsDone(false)
      } catch (error) {
        console.warn('Failed to remove from localStorage:', error)
      }
    }
  }

  // Get all completed verses
  const getAllCompleted = () => {
    if (typeof window === 'undefined') return []

    try {
      const completed = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith('verse_done_') && localStorage.getItem(key) === 'true') {
          const slug = key.replace('verse_done_', '')
          completed.push(slug)
        }
      }
      return completed
    } catch (error) {
      console.warn('Failed to read completed verses from localStorage:', error)
      return []
    }
  }

  // Get completion statistics
  const getStats = (totalVerses = 0) => {
    const completed = getAllCompleted()
    return {
      completed: completed.length,
      total: totalVerses,
      percentage: totalVerses > 0 ? Math.round((completed.length / totalVerses) * 100) : 0,
      completedSlugs: completed,
    }
  }

  return {
    isDone,
    isLoaded,
    markDone,
    markUndone,
    toggleDone,
    reset,
    getAllCompleted,
    getStats,
  }
}

/**
 * Custom hook for managing challenge progress and tracking
 * @returns {Object} Object containing challenge-related functions
 */
export function useChallengeProgress() {
  const [currentChallenge, setCurrentChallenge] = useState(null)
  const [isLoaded, setIsLoaded] = useState(false)

  // Initialize challenge state from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem('current_challenge')
        if (stored) {
          setCurrentChallenge(JSON.parse(stored))
        }
      } catch (error) {
        console.warn('Failed to read challenge from localStorage:', error)
      } finally {
        setIsLoaded(true)
      }
    }
  }, [])

  // Start a new challenge
  const startChallenge = (challengeId) => {
    if (typeof window !== 'undefined') {
      try {
        const challengeData = {
          id: challengeId,
          startDate: new Date().toISOString(),
          currentDay: 1,
          completedDays: [],
          isActive: true,
        }
        localStorage.setItem('current_challenge', JSON.stringify(challengeData))
        setCurrentChallenge(challengeData)
        return challengeData
      } catch (error) {
        console.warn('Failed to start challenge:', error)
        return null
      }
    }
  }

  // Complete a challenge day
  const completeDay = (day) => {
    if (typeof window !== 'undefined' && currentChallenge) {
      try {
        const updatedChallenge = {
          ...currentChallenge,
          completedDays: [...new Set([...currentChallenge.completedDays, day])],
          currentDay: Math.min(day + 1, 7),
          lastCompletedDate: new Date().toISOString(),
        }

        // Mark challenge as completed if all 7 days are done
        if (updatedChallenge.completedDays.length === 7) {
          updatedChallenge.isActive = false
          updatedChallenge.completedDate = new Date().toISOString()

          // Add to challenge history
          const history = getChallengeHistory()
          const completedChallenge = { ...updatedChallenge }
          localStorage.setItem(
            'challenge_history',
            JSON.stringify([...history, completedChallenge])
          )
        }

        localStorage.setItem('current_challenge', JSON.stringify(updatedChallenge))
        setCurrentChallenge(updatedChallenge)
        return updatedChallenge
      } catch (error) {
        console.warn('Failed to complete challenge day:', error)
        return null
      }
    }
  }

  // Reset current challenge
  const resetChallenge = () => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem('current_challenge')
        setCurrentChallenge(null)
      } catch (error) {
        console.warn('Failed to reset challenge:', error)
      }
    }
  }

  // Get challenge history
  const getChallengeHistory = () => {
    if (typeof window === 'undefined') return []

    try {
      const history = localStorage.getItem('challenge_history')
      return history ? JSON.parse(history) : []
    } catch (error) {
      console.warn('Failed to read challenge history:', error)
      return []
    }
  }

  // Get challenge statistics
  const getChallengeStats = () => {
    const history = getChallengeHistory()
    const completedChallenges = history.filter((c) => c.completedDate)

    return {
      totalChallenges: history.length,
      completedChallenges: completedChallenges.length,
      currentStreak: calculateStreak(history),
      totalDaysCompleted: history.reduce(
        (total, challenge) => total + challenge.completedDays.length,
        0
      ),
    }
  }

  // Calculate current streak
  const calculateStreak = (history) => {
    if (history.length === 0) return 0

    const sortedHistory = history
      .filter((c) => c.completedDate)
      .sort((a, b) => new Date(b.completedDate) - new Date(a.completedDate))

    let streak = 0
    let lastDate = new Date()

    for (const challenge of sortedHistory) {
      const challengeDate = new Date(challenge.completedDate)
      const daysDiff = Math.floor((lastDate - challengeDate) / (1000 * 60 * 60 * 24))

      if (daysDiff <= 14) {
        // Allow up to 2 weeks gap
        streak++
        lastDate = challengeDate
      } else {
        break
      }
    }

    return streak
  }

  // Check if a specific day is completed
  const isDayCompleted = (day) => {
    return currentChallenge?.completedDays.includes(day) || false
  }

  // Get current challenge progress
  const getChallengeProgress = () => {
    if (!currentChallenge) return null

    return {
      challengeId: currentChallenge.id,
      currentDay: currentChallenge.currentDay,
      completedDays: currentChallenge.completedDays,
      progress: (currentChallenge.completedDays.length / 7) * 100,
      isActive: currentChallenge.isActive,
      startDate: currentChallenge.startDate,
      daysRemaining: 7 - currentChallenge.completedDays.length,
    }
  }

  return {
    currentChallenge,
    isLoaded,
    startChallenge,
    completeDay,
    resetChallenge,
    getChallengeHistory,
    getChallengeStats,
    isDayCompleted,
    getChallengeProgress,
  }
}
