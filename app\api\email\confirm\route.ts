import { NextRequest, NextResponse } from 'next/server'
import { SubscriptionManager } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return NextResponse.json({ error: 'Confirmation token is required' }, { status: 400 })
    }

    const subscriptionManager = new SubscriptionManager()

    // First, try to find a subscription with this confirmation token
    let subscription = subscriptionManager.getSubscriptionByConfirmationToken(token)

    if (!subscription) {
      // If no subscription found with this token, it might be an already confirmed subscription
      // Check if this token was used before by looking for any subscription that might have used it
      // Since we can't track old tokens, we'll redirect to a generic "already confirmed" page
      return NextResponse.redirect(
        new URL('/email/confirmed?success=true&already_confirmed=true', request.url)
      )
    }

    if (subscription.isConfirmed) {
      // Subscription is already confirmed, redirect to success page
      return NextResponse.redirect(
        new URL('/email/confirmed?success=true&already_confirmed=true', request.url)
      )
    }

    const success = subscriptionManager.confirmSubscription(token)

    if (success) {
      // Redirect to a confirmation success page
      return NextResponse.redirect(new URL('/email/confirmed?success=true', request.url))
    } else {
      return NextResponse.json({ error: 'Failed to confirm subscription' }, { status: 500 })
    }
  } catch (error) {
    console.error('Email confirmation error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
