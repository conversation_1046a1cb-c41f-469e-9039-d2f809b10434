import { NextRequest, NextResponse } from 'next/server'
import { SubscriptionManager } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return NextResponse.json({ error: 'Confirmation token is required' }, { status: 400 })
    }

    const subscriptionManager = new SubscriptionManager()
    const subscription = subscriptionManager.getSubscriptionByConfirmationToken(token)

    if (!subscription) {
      return NextResponse.json({ error: 'Invalid or expired confirmation token' }, { status: 404 })
    }

    if (subscription.isConfirmed) {
      return NextResponse.json({ message: 'Email already confirmed' }, { status: 200 })
    }

    const success = subscriptionManager.confirmSubscription(token)

    if (success) {
      // Redirect to a confirmation success page
      return NextResponse.redirect(new URL('/email/confirmed?success=true', request.url))
    } else {
      return NextResponse.json({ error: 'Failed to confirm subscription' }, { status: 500 })
    }
  } catch (error) {
    console.error('Email confirmation error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
