import { NextRequest, NextResponse } from 'next/server'

export const runtime = 'edge'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    // Always redirect to success page for now to test if route works
    if (!token) {
      return NextResponse.redirect(
        new URL('/email/confirmed?success=true&debug=no_token', request.url)
      )
    }

    // For any token, redirect to already confirmed page to test
    return NextResponse.redirect(
      new URL(
        '/email/confirmed?success=true&already_confirmed=true&debug=token_present',
        request.url
      )
    )
  } catch (error) {
    // Return JSON error instead of redirect to see what's happening
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      },
      { status: 500 }
    )
  }
}
