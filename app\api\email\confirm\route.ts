import { NextRequest, NextResponse } from 'next/server'
import { SubscriptionManager } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return NextResponse.json({ error: 'Confirmation token is required' }, { status: 400 })
    }

    const subscriptionManager = new SubscriptionManager()

    // Use the enhanced confirmation method for better handling
    const result = subscriptionManager.confirmSubscriptionWithDetails(token)

    if (!result.found) {
      if (result.alreadyConfirmed) {
        // There are confirmed subscriptions, so this token was likely already used
        return NextResponse.redirect(
          new URL('/email/confirmed?success=true&already_confirmed=true', request.url)
        )
      } else {
        // No confirmed subscriptions found, this is likely an invalid token
        return NextResponse.json(
          { error: 'Invalid or expired confirmation token' },
          { status: 404 }
        )
      }
    }

    if (result.alreadyConfirmed) {
      // Subscription was already confirmed
      return NextResponse.redirect(
        new URL('/email/confirmed?success=true&already_confirmed=true', request.url)
      )
    }

    if (result.success) {
      // Confirmation was successful
      return NextResponse.redirect(new URL('/email/confirmed?success=true', request.url))
    } else {
      // Confirmation failed
      return NextResponse.json({ error: 'Failed to confirm subscription' }, { status: 500 })
    }
  } catch (error) {
    console.error('Email confirmation error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
