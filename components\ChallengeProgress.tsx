'use client'

import { useChallengeProgress } from '@/hooks/useProgress'
import { getChallengeById } from '@/data/challenges'
import { CheckIcon, FireIcon, StarIcon } from '@heroicons/react/24/outline'
import { CheckIcon as CheckIconSolid } from '@heroicons/react/24/solid'

interface ChallengeProgressProps {
  challengeId?: string
  showDetails?: boolean
}

export default function ChallengeProgress({
  challengeId,
  showDetails = true,
}: ChallengeProgressProps) {
  const { currentChallenge, getChallengeProgress, getChallengeStats } = useChallengeProgress()

  const progress = getChallengeProgress()
  const stats = getChallengeStats()
  const challenge = challengeId
    ? getChallengeById(challengeId)
    : currentChallenge
      ? getChallengeById(currentChallenge.id)
      : null

  if (!progress || !challenge) {
    return null
  }

  const progressPercentage = (progress.completedDays.length / 7) * 100

  return (
    <div className="rounded-lg border border-amber-200 bg-gradient-to-r from-amber-50 to-orange-50 p-6 dark:border-amber-700 dark:from-amber-900/20 dark:to-orange-900/20">
      {/* Challenge Header */}
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-amber-100 dark:bg-amber-900/30">
            <StarIcon className="h-5 w-5 text-amber-600 dark:text-amber-400" />
          </div>
          <div className="mr-3">
            <h3 className="font-semibold text-gray-900 dark:text-gray-100">{challenge.title}</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              اليوم {progress.currentDay} من {challenge.duration}
            </p>
          </div>
        </div>

        {stats.currentStreak > 0 && (
          <div className="flex items-center text-orange-600 dark:text-orange-400">
            <FireIcon className="ml-1 h-4 w-4" />
            <span className="text-sm font-medium">{stats.currentStreak}</span>
          </div>
        )}
      </div>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="mb-2 flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">التقدم</span>
          <span className="font-medium text-gray-900 dark:text-gray-100">
            {progress.completedDays.length}/7 أيام
          </span>
        </div>
        <div className="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700">
          <div
            className="h-2 rounded-full bg-gradient-to-r from-amber-500 to-orange-500 transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>

      {/* Days Grid */}
      {showDetails && (
        <div className="grid grid-cols-7 gap-2">
          {Array.from({ length: 7 }, (_, i) => {
            const dayNumber = i + 1
            const isCompleted = progress.completedDays.includes(dayNumber)
            const isCurrent = dayNumber === progress.currentDay && progress.isActive

            return (
              <div
                key={dayNumber}
                className={`flex h-8 w-8 items-center justify-center rounded-full text-xs font-medium transition-colors ${
                  isCompleted
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                    : isCurrent
                      ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400'
                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                }`}
              >
                {isCompleted ? <CheckIconSolid className="h-3 w-3" /> : dayNumber}
              </div>
            )
          })}
        </div>
      )}

      {/* Completion Message */}
      {progress.completedDays.length === 7 && (
        <div className="mt-4 rounded-lg bg-green-50 p-3 dark:bg-green-900/20">
          <div className="flex items-center">
            <CheckIcon className="ml-2 h-5 w-5 text-green-600 dark:text-green-400" />
            <span className="font-medium text-green-800 dark:text-green-400">
              مبروك! أكملت التحدي بنجاح! 🎉
            </span>
          </div>
        </div>
      )}
    </div>
  )
}
