'use client'

import { useLanguage } from '@/contexts/LanguageContext'
import { useEffect } from 'react'

interface LanguageWrapperProps {
  children: React.ReactNode
}

export default function LanguageWrapper({ children }: LanguageWrapperProps) {
  const { language, isRTL } = useLanguage()

  useEffect(() => {
    // Update document attributes when language changes
    document.documentElement.lang = language
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr'

    // Update body classes for proper styling
    const body = document.body
    if (isRTL) {
      body.classList.add('rtl')
      body.classList.remove('ltr')
    } else {
      body.classList.add('ltr')
      body.classList.remove('rtl')
    }
  }, [language, isRTL])

  return <>{children}</>
}
