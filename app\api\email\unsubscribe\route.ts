import { NextRequest, NextResponse } from 'next/server'
import { SubscriptionManager } from '@/lib/database'
import { redirect } from 'next/navigation'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return NextResponse.json({ error: 'Unsubscribe token is required' }, { status: 400 })
    }

    const subscriptionManager = new SubscriptionManager()
    const subscription = subscriptionManager.getSubscriptionByUnsubscribeToken(token)

    if (!subscription) {
      return NextResponse.json({ error: 'Invalid unsubscribe token' }, { status: 404 })
    }

    if (!subscription.isActive) {
      // Already unsubscribed, redirect to confirmation page
      return redirect('/email/unsubscribed?already=true')
    }

    const success = subscriptionManager.unsubscribe(token)

    if (success) {
      // Redirect to unsubscribe confirmation page
      return redirect('/email/unsubscribed?success=true')
    } else {
      return NextResponse.json({ error: 'Failed to unsubscribe' }, { status: 500 })
    }
  } catch (error) {
    console.error('Unsubscribe error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { token } = body

    if (!token) {
      return NextResponse.json({ error: 'Unsubscribe token is required' }, { status: 400 })
    }

    const subscriptionManager = new SubscriptionManager()
    const subscription = subscriptionManager.getSubscriptionByUnsubscribeToken(token)

    if (!subscription) {
      return NextResponse.json({ error: 'Invalid unsubscribe token' }, { status: 404 })
    }

    if (!subscription.isActive) {
      return NextResponse.json({ message: 'Already unsubscribed' }, { status: 200 })
    }

    const success = subscriptionManager.unsubscribe(token)

    if (success) {
      return NextResponse.json({
        message: 'Successfully unsubscribed',
        success: true,
      })
    } else {
      return NextResponse.json({ error: 'Failed to unsubscribe' }, { status: 500 })
    }
  } catch (error) {
    console.error('Unsubscribe error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
