'use client'

import { useState } from 'react'
import { useTranslation } from '@/hooks/useTranslation'
import { EnvelopeIcon, CheckCircleIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline'

interface NewsletterSubscriptionProps {
  className?: string
  showTitle?: boolean
  compact?: boolean
}

export default function NewsletterSubscription({
  className = '',
  showTitle = true,
  compact = false,
}: NewsletterSubscriptionProps) {
  const { t, language } = useTranslation()
  const [email, setEmail] = useState('')
  const [frequency, setFrequency] = useState<'daily' | 'weekly'>('daily')
  const [isLoading, setIsLoading] = useState(false)
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [message, setMessage] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email.trim()) {
      setStatus('error')
      setMessage(t('newsletter.emailRequired'))
      return
    }

    setIsLoading(true)
    setStatus('idle')

    try {
      const response = await fetch('/api/newsletter', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'subscribe',
          email: email.trim(),
          frequency,
          language,
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setStatus('success')
        setMessage(t('newsletter.subscriptionSuccess'))
        setEmail('')
      } else {
        setStatus('error')
        setMessage(data.error || t('newsletter.subscriptionError'))
      }
    } catch (error) {
      setStatus('error')
      setMessage(t('newsletter.networkError'))
    } finally {
      setIsLoading(false)
    }
  }

  const resetStatus = () => {
    setStatus('idle')
    setMessage('')
  }

  if (compact) {
    return (
      <div
        className={`rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800 ${className}`}
      >
        <form onSubmit={handleSubmit} className="space-y-3">
          <div className="flex flex-col gap-2 sm:flex-row">
            <input
              type="email"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value)
                resetStatus()
              }}
              placeholder={t('newsletter.emailPlaceholder')}
              className="flex-1 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              disabled={isLoading}
            />
            <button
              type="submit"
              disabled={isLoading}
              className="flex items-center justify-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none disabled:opacity-50 dark:focus:ring-offset-gray-800"
            >
              {isLoading ? (
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
              ) : (
                <EnvelopeIcon className="h-4 w-4" />
              )}
              {t('newsletter.subscribe')}
            </button>
          </div>

          <div className="flex gap-4 text-sm">
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="frequency"
                value="daily"
                checked={frequency === 'daily'}
                onChange={(e) => setFrequency(e.target.value as 'daily')}
                className="text-blue-600 focus:ring-blue-500"
              />
              {t('newsletter.daily')}
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="frequency"
                value="weekly"
                checked={frequency === 'weekly'}
                onChange={(e) => setFrequency(e.target.value as 'weekly')}
                className="text-blue-600 focus:ring-blue-500"
              />
              {t('newsletter.weekly')}
            </label>
          </div>

          {status !== 'idle' && (
            <div
              className={`flex items-center gap-2 text-sm ${
                status === 'success'
                  ? 'text-green-600 dark:text-green-400'
                  : 'text-red-600 dark:text-red-400'
              }`}
            >
              {status === 'success' ? (
                <CheckCircleIcon className="h-4 w-4" />
              ) : (
                <ExclamationCircleIcon className="h-4 w-4" />
              )}
              {message}
            </div>
          )}
        </form>
      </div>
    )
  }

  return (
    <div
      className={`rounded-lg border border-gray-200 bg-gradient-to-br from-blue-50 to-indigo-50 p-6 dark:border-gray-700 dark:from-blue-900/20 dark:to-indigo-900/20 ${className}`}
    >
      {showTitle && (
        <div className="mb-6 text-center">
          <h3 className="mb-2 text-xl font-bold text-gray-900 dark:text-gray-100">
            {t('newsletter.title')}
          </h3>
          <p className="text-gray-600 dark:text-gray-400">{t('newsletter.description')}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label
            htmlFor="email"
            className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            {t('newsletter.emailLabel')}
          </label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => {
              setEmail(e.target.value)
              resetStatus()
            }}
            placeholder={t('newsletter.emailPlaceholder')}
            className="w-full rounded-md border border-gray-300 px-4 py-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            disabled={isLoading}
          />
        </div>

        <div>
          <label className="mb-3 block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t('newsletter.frequencyLabel')}
          </label>
          <div className="grid grid-cols-2 gap-3">
            <label
              htmlFor="daily"
              className="flex cursor-pointer items-center gap-3 rounded-lg border border-gray-200 p-3 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-700"
            >
              <input
                type="radio"
                name="frequency"
                id="daily"
                value="daily"
                checked={frequency === 'daily'}
                onChange={(e) => setFrequency(e.target.value as 'daily')}
                className="text-blue-600 focus:ring-blue-500"
              />
              <div>
                <div className="font-medium text-gray-900 dark:text-gray-100">
                  {t('newsletter.daily')}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {t('newsletter.dailyDescription')}
                </div>
              </div>
            </label>

            <label
              htmlFor="weekly"
              className="flex cursor-pointer items-center gap-3 rounded-lg border border-gray-200 p-3 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-700"
            >
              <input
                type="radio"
                name="frequency"
                id="weekly"
                value="weekly"
                checked={frequency === 'weekly'}
                onChange={(e) => setFrequency(e.target.value as 'weekly')}
                className="text-blue-600 focus:ring-blue-500"
              />
              <div>
                <div className="font-medium text-gray-900 dark:text-gray-100">
                  {t('newsletter.weekly')}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {t('newsletter.weeklyDescription')}
                </div>
              </div>
            </label>
          </div>
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className="flex w-full items-center justify-center gap-2 rounded-md bg-blue-600 px-6 py-3 font-medium text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none disabled:opacity-50 dark:focus:ring-offset-gray-800"
        >
          {isLoading ? (
            <div className="h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent" />
          ) : (
            <EnvelopeIcon className="h-5 w-5" />
          )}
          {t('newsletter.subscribe')}
        </button>

        {status !== 'idle' && (
          <div
            className={`flex items-center gap-2 rounded-md p-3 ${
              status === 'success'
                ? 'bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400'
                : 'bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-400'
            }`}
          >
            {status === 'success' ? (
              <CheckCircleIcon className="h-5 w-5" />
            ) : (
              <ExclamationCircleIcon className="h-5 w-5" />
            )}
            {message}
          </div>
        )}

        <p className="text-center text-xs text-gray-500 dark:text-gray-400">
          {t('newsletter.privacyNote')}
        </p>
      </form>
    </div>
  )
}
