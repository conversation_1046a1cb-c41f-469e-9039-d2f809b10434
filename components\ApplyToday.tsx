'use client'

import { useProgress, useChallengeProgress } from '@/hooks/useProgress'
import { CheckIcon, ClockIcon, StarIcon, FireIcon } from '@heroicons/react/24/outline'
import { getChallengeById, getChallengeDayData } from '@/data/challenges'
import { useTranslation } from '@/hooks/useTranslation'

interface ApplyTodayProps {
  slug: string
  lesson: string
  reflection: string
  challengeMode?: boolean
  challengeId?: string
  challengeDay?: number
}

export default function ApplyToday({
  slug,
  lesson,
  reflection,
  challengeMode = false,
  challengeId,
  challengeDay,
}: ApplyTodayProps) {
  const { t } = useTranslation()
  const { isDone, isLoaded, toggleDone } = useProgress(slug)
  const { currentChallenge, completeDay, isDayCompleted, getChallengeProgress } =
    useChallengeProgress()

  // Get challenge data if in challenge mode
  const challengeData = challengeMode && challengeId ? getChallengeById(challengeId) : null
  const dayData =
    challengeMode && challengeId && challengeDay
      ? getChallengeDayData(challengeId, challengeDay)
      : null

  // Determine if this is a challenge day completion
  const isChallengeDay = challengeMode && challengeDay && currentChallenge?.id === challengeId
  const isDayDone = isChallengeDay ? isDayCompleted(challengeDay) : isDone

  // Handle completion - either verse or challenge day
  const handleComplete = () => {
    if (isChallengeDay && challengeDay) {
      completeDay(challengeDay)
    } else {
      toggleDone()
    }
  }

  if (!isLoaded) {
    return (
      <div className="rounded-lg border border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-6 dark:border-gray-700 dark:from-blue-900/20 dark:to-indigo-900/20">
        <div className="animate-pulse">
          <div className="h-6 w-32 rounded bg-gray-200 dark:bg-gray-700"></div>
          <div className="mt-4 h-4 w-full rounded bg-gray-200 dark:bg-gray-700"></div>
          <div className="mt-2 h-4 w-3/4 rounded bg-gray-200 dark:bg-gray-700"></div>
        </div>
      </div>
    )
  }

  // Use challenge data if available, otherwise use regular lesson data
  const displayLesson = dayData?.task || lesson
  const displayReflection = dayData?.reflection || reflection
  const displayTitle = challengeMode
    ? `${t('challenges.currentDay')} ${challengeDay}: ${dayData?.title || t('challenges.todaysTask')}`
    : t('verse.practicalLesson')
  const estimatedTime = dayData?.estimatedTime
  const criteria = dayData?.criteria

  return (
    <div
      className={`rounded-lg border p-6 ${
        challengeMode
          ? 'border-amber-200 bg-gradient-to-r from-amber-50 to-orange-50 dark:border-amber-700 dark:from-amber-900/20 dark:to-orange-900/20'
          : 'border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 dark:border-gray-700 dark:from-blue-900/20 dark:to-indigo-900/20'
      }`}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="mb-4 flex items-center text-lg font-semibold text-gray-900 dark:text-gray-100">
            {challengeMode ? (
              <StarIcon className="ml-2 h-5 w-5 text-amber-600 dark:text-amber-400" />
            ) : (
              <ClockIcon className="ml-2 h-5 w-5 text-blue-600 dark:text-blue-400" />
            )}
            {displayTitle}
            {challengeMode && estimatedTime && (
              <span className="mr-2 text-sm font-normal text-gray-600 dark:text-gray-400">
                ({estimatedTime})
              </span>
            )}
          </h3>

          <div className="space-y-4">
            <div>
              <h4 className="mb-2 font-medium text-gray-800 dark:text-gray-200">
                {challengeMode ? t('verse.task') : t('verse.practicalLesson')}
              </h4>
              <p className="leading-relaxed text-gray-700 dark:text-gray-300">{displayLesson}</p>
            </div>

            {criteria && challengeMode && (
              <div>
                <h4 className="mb-2 font-medium text-gray-800 dark:text-gray-200">
                  {t('verse.completionCriteria')}
                </h4>
                <p className="leading-relaxed text-gray-700 dark:text-gray-300">{criteria}</p>
              </div>
            )}

            <div>
              <h4 className="mb-2 font-medium text-gray-800 dark:text-gray-200">
                {t('verse.reflection')}
              </h4>
              <p className="leading-relaxed text-gray-700 dark:text-gray-300">
                {displayReflection}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 flex items-center justify-between border-t border-gray-200 pt-4 dark:border-gray-600">
        <div className="flex items-center">
          {isDayDone ? (
            <div className="flex items-center text-green-600 dark:text-green-400">
              <CheckIcon className="ml-2 h-5 w-5" />
              <span className="font-medium">
                {challengeMode ? t('verse.dayCompleted') : t('verse.completed')}
              </span>
              {challengeMode && <FireIcon className="mr-2 h-4 w-4 text-amber-500" />}
            </div>
          ) : (
            <span className="text-gray-600 dark:text-gray-400">
              {challengeMode ? t('verse.didYouCompleteTask') : t('verse.didYouApply')}
            </span>
          )}
        </div>

        <button
          onClick={handleComplete}
          className={`rounded-lg px-4 py-2 font-medium transition-colors ${
            isDayDone
              ? 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50'
              : challengeMode
                ? 'bg-amber-600 text-white hover:bg-amber-700 dark:bg-amber-500 dark:hover:bg-amber-600'
                : 'bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600'
          }`}
          aria-label={isDayDone ? t('verse.undoComplete') : t('verse.markComplete')}
        >
          {isDayDone
            ? t('verse.undoComplete')
            : challengeMode
              ? t('verse.taskComplete')
              : t('verse.markComplete')}
        </button>
      </div>
    </div>
  )
}
