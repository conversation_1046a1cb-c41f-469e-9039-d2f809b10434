@import 'tailwindcss';
@plugin "@tailwindcss/forms";
@plugin '@tailwindcss/typography';
@source '../node_modules/pliny';
@custom-variant dark (&:where(.dark, .dark *));

/* Core theme configuration */
@theme {
  /* Font families */
  --font-sans: var(--font-cairo), var(--font-space-grotesk), ui-sans-serif, system-ui, sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-arabic: var(--font-cairo), '<PERSON><PERSON>', 'Scheherazade New', serif;

  /* Colors */
  /* Copied from https://tailwindcss.com/docs/theme#default-theme-variable-reference */
  --color-primary-50: oklch(0.971 0.014 343.198);
  --color-primary-100: oklch(0.948 0.028 342.258);
  --color-primary-200: oklch(0.899 0.061 343.231);
  --color-primary-300: oklch(0.823 0.12 346.018);
  --color-primary-400: oklch(0.718 0.202 349.761);
  --color-primary-500: oklch(0.656 0.241 354.308);
  --color-primary-600: oklch(0.592 0.249 0.584);
  --color-primary-700: oklch(0.525 0.223 3.958);
  --color-primary-800: oklch(0.459 0.187 3.815);
  --color-primary-900: oklch(0.408 0.153 2.432);
  --color-primary-950: oklch(0.284 0.109 3.907);

  --color-gray-50: oklch(0.985 0.002 247.839);
  --color-gray-100: oklch(0.967 0.003 264.542);
  --color-gray-200: oklch(0.928 0.006 264.531);
  --color-gray-300: oklch(0.872 0.01 258.338);
  --color-gray-400: oklch(0.707 0.022 261.325);
  --color-gray-500: oklch(0.551 0.027 264.364);
  --color-gray-600: oklch(0.446 0.03 256.802);
  --color-gray-700: oklch(0.373 0.034 259.733);
  --color-gray-800: oklch(0.278 0.033 256.848);
  --color-gray-900: oklch(0.21 0.034 264.665);
  --color-gray-950: oklch(0.13 0.028 261.692);

  /* Line heights */
  --line-height-11: 2.75rem;
  --line-height-12: 3rem;
  --line-height-13: 3.25rem;
  --line-height-14: 3.5rem;

  /* Z-index values */
  --z-60: 60;
  --z-70: 70;
  --z-80: 80;
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }

  a,
  button {
    outline-color: var(--color-primary-500);
  }

  a:focus-visible,
  button:focus-visible {
    outline: 2px solid;
    border-radius: var(--radius-sm);
    outline-color: var(--color-primary-500);
  }
}

@layer utilities {
  .prose {
    & a {
      color: var(--color-primary-500);
      &:hover {
        color: var(--color-primary-600);
      }
      & code {
        color: var(--color-primary-400);
      }
    }
    & :where(h1, h2) {
      font-weight: 700;
      letter-spacing: var(--tracking-tight);
    }
    & h3 {
      font-weight: 600;
    }
    & :where(code):not(pre code) {
      color: var(--color-indigo-500);
    }
  }

  .prose-invert {
    & a {
      color: var(--color-primary-500);
      &:hover {
        color: var(--color-primary-400);
      }
      & code {
        color: var(--color-primary-400);
      }
    }
    & :where(h1, h2, h3, h4, h5, h6) {
      color: var(--color-gray-100);
    }
  }
}

.task-list-item::before {
  @apply hidden;
}

.task-list-item {
  @apply list-none;
}

.footnotes {
  @apply mt-12 border-t border-gray-200 pt-8 dark:border-gray-700;
}

.data-footnote-backref {
  @apply no-underline;
}

.csl-entry {
  @apply my-5;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* https://stackoverflow.com/questions/61083813/how-to-avoid-internal-autofill-selected-style-to-be-applied */
input:-webkit-autofill,
input:-webkit-autofill:focus {
  transition:
    background-color 600000s 0s,
    color 600000s 0s;
}

.katex-display {
  overflow: auto hidden;
}

.content-header-link {
  opacity: 0;
  margin-left: -24px;
  padding-right: 4px;
}

.content-header:hover .content-header-link,
.content-header-link:hover {
  opacity: 1;
}

.linkicon {
  display: inline-block;
  vertical-align: middle;
}

/* RTL Support */
[dir='rtl'] .prose {
  text-align: right;
}

[dir='rtl'] .prose h1,
[dir='rtl'] .prose h2,
[dir='rtl'] .prose h3,
[dir='rtl'] .prose h4,
[dir='rtl'] .prose h5,
[dir='rtl'] .prose h6 {
  text-align: right;
}

[dir='rtl'] .prose ul,
[dir='rtl'] .prose ol {
  padding-right: 1.625em;
  padding-left: 0;
}

[dir='rtl'] .prose blockquote {
  border-right: 0.25rem solid var(--color-gray-300);
  border-left: none;
  padding-right: 1em;
  padding-left: 0;
}

/* Arabic text styling */
.arabic-text {
  font-family: var(--font-arabic);
  line-height: 2;
  font-size: 1.125rem;
}

/* Verse text styling */
.verse-text {
  font-family: var(--font-arabic);
  font-size: 1.5rem;
  line-height: 2.5;
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 1rem;
  border: 2px solid #cbd5e1;
  margin: 2rem 0;
}

.dark .verse-text {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border-color: #475569;
  color: #f1f5f9;
}
