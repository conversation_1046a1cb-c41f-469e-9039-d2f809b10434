import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    // Verify the request is authorized (you might want to add webhook signature verification)
    const authHeader = request.headers.get('authorization')
    const expectedAuth = process.env.WEBHOOK_SECRET || process.env.CRON_SECRET || 'your-secret-key'

    if (authHeader !== `Bearer ${expectedAuth}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { verseSlug } = body

    if (!verseSlug) {
      return NextResponse.json({ error: 'Verse slug is required' }, { status: 400 })
    }

    // Call the email notification API to send new verse notifications
    const baseUrl = process.env.VERCEL_URL
      ? `https://${process.env.VERCEL_URL}`
      : 'https://quran-verses-gilt.vercel.app'

    const response = await fetch(`${baseUrl}/api/email/notify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${expectedAuth}`,
      },
      body: JSON.stringify({
        type: 'new_verse',
        verseSlug,
      }),
    })

    const result = await response.json()

    if (response.ok) {
      return NextResponse.json({
        success: true,
        message: 'New verse notifications sent successfully',
        ...result,
      })
    } else {
      return NextResponse.json(
        { error: 'Failed to send new verse notifications', details: result },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('New verse webhook error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
