import { getAllVerses } from './verses'
import { SubscriptionManager, EmailLogger } from './database'
import { EmailService } from './email-service'
import siteMetadata from '@/data/siteMetadata'

// Utility functions for email notifications

export interface NotificationResult {
  success: boolean
  sent: number
  failed: number
  totalSubscriptions: number
  verse?: {
    title: string
    slug: string
  }
  error?: string
}

export class NotificationManager {
  private subscriptionManager: SubscriptionManager
  private emailService: EmailService
  private emailLogger: EmailLogger

  constructor() {
    this.subscriptionManager = new SubscriptionManager()
    this.emailService = new EmailService()
    this.emailLogger = new EmailLogger()
  }

  // Send notifications for a new verse
  async sendNewVerseNotifications(verseSlug: string): Promise<NotificationResult> {
    try {
      // Get the verse
      const allVerses = getAllVerses()
      const verse = allVerses.find((v) => v.slug === verseSlug)

      if (!verse) {
        return {
          success: false,
          sent: 0,
          failed: 0,
          totalSubscriptions: 0,
          error: 'Verse not found',
        }
      }

      // Get daily subscribers (new verses go to daily subscribers)
      const subscriptions = this.subscriptionManager.getActiveSubscriptionsByFrequency('daily')

      if (subscriptions.length === 0) {
        return {
          success: true,
          sent: 0,
          failed: 0,
          totalSubscriptions: 0,
          verse: {
            title: verse.title,
            slug: verse.slug,
          },
        }
      }

      // Prepare verse data for email
      const verseEmailData = {
        title: verse.title,
        surah: verse.surah,
        ayah: verse.ayah,
        slug: verse.slug,
        lesson: verse.lesson,
        reflection: verse.reflection,
        url: `${siteMetadata.siteUrl}/verses/${verse.slug}`,
      }

      // Send emails
      let successCount = 0
      let failureCount = 0

      for (const subscription of subscriptions) {
        try {
          const success = await this.emailService.sendVerseNotification(
            subscription,
            verseEmailData,
            'new_verse'
          )

          if (success) {
            successCount++
            this.subscriptionManager.updateLastEmailSent(subscription.id)
          } else {
            failureCount++
          }

          // Add delay to avoid overwhelming email service
          await new Promise((resolve) => setTimeout(resolve, 100))
        } catch (error) {
          console.error(`Failed to send email to ${subscription.email}:`, error)
          failureCount++
        }
      }

      return {
        success: true,
        sent: successCount,
        failed: failureCount,
        totalSubscriptions: subscriptions.length,
        verse: {
          title: verse.title,
          slug: verse.slug,
        },
      }
    } catch (error) {
      console.error('Error sending new verse notifications:', error)
      return {
        success: false,
        sent: 0,
        failed: 0,
        totalSubscriptions: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  // Send daily reminder notifications
  async sendDailyReminders(): Promise<NotificationResult> {
    try {
      // Get today's verse
      const { getTodaysVerse } = await import('./verses')
      const verse = getTodaysVerse()

      if (!verse) {
        return {
          success: false,
          sent: 0,
          failed: 0,
          totalSubscriptions: 0,
          error: 'No verse available for today',
        }
      }

      // Get daily subscribers
      const subscriptions = this.subscriptionManager.getActiveSubscriptionsByFrequency('daily')

      if (subscriptions.length === 0) {
        return {
          success: true,
          sent: 0,
          failed: 0,
          totalSubscriptions: 0,
          verse: {
            title: verse.title,
            slug: verse.slug,
          },
        }
      }

      // Filter out subscribers who already received an email today
      const today = new Date().toISOString().split('T')[0]
      const subscribersToEmail = subscriptions.filter((sub) => {
        if (!sub.lastEmailSent) return true
        const lastEmailDate = new Date(sub.lastEmailSent).toISOString().split('T')[0]
        return lastEmailDate !== today
      })

      // Prepare verse data for email
      const verseEmailData = {
        title: verse.title,
        surah: verse.surah,
        ayah: verse.ayah,
        slug: verse.slug,
        lesson: verse.lesson,
        reflection: verse.reflection,
        url: `${siteMetadata.siteUrl}/verses/${verse.slug}`,
      }

      // Send emails
      let successCount = 0
      let failureCount = 0

      for (const subscription of subscribersToEmail) {
        try {
          const success = await this.emailService.sendVerseNotification(
            subscription,
            verseEmailData,
            'daily_reminder'
          )

          if (success) {
            successCount++
            this.subscriptionManager.updateLastEmailSent(subscription.id)
          } else {
            failureCount++
          }

          // Add delay to avoid overwhelming email service
          await new Promise((resolve) => setTimeout(resolve, 100))
        } catch (error) {
          console.error(`Failed to send email to ${subscription.email}:`, error)
          failureCount++
        }
      }

      return {
        success: true,
        sent: successCount,
        failed: failureCount,
        totalSubscriptions: subscriptions.length,
        verse: {
          title: verse.title,
          slug: verse.slug,
        },
      }
    } catch (error) {
      console.error('Error sending daily reminders:', error)
      return {
        success: false,
        sent: 0,
        failed: 0,
        totalSubscriptions: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  // Send weekly reminder notifications
  async sendWeeklyReminders(): Promise<NotificationResult> {
    try {
      // Get today's verse for weekly reminder
      const { getTodaysVerse } = await import('./verses')
      const verse = getTodaysVerse()

      if (!verse) {
        return {
          success: false,
          sent: 0,
          failed: 0,
          totalSubscriptions: 0,
          error: 'No verse available for weekly reminder',
        }
      }

      // Get weekly subscribers
      const subscriptions = this.subscriptionManager.getActiveSubscriptionsByFrequency('weekly')

      if (subscriptions.length === 0) {
        return {
          success: true,
          sent: 0,
          failed: 0,
          totalSubscriptions: 0,
          verse: {
            title: verse.title,
            slug: verse.slug,
          },
        }
      }

      // Filter out subscribers who already received an email this week
      const now = new Date()
      const weekStart = new Date(now.setDate(now.getDate() - now.getDay()))
      const weekStartStr = weekStart.toISOString().split('T')[0]

      const subscribersToEmail = subscriptions.filter((sub) => {
        if (!sub.lastEmailSent) return true
        const lastEmailDate = new Date(sub.lastEmailSent).toISOString().split('T')[0]
        return lastEmailDate < weekStartStr
      })

      // Prepare verse data for email
      const verseEmailData = {
        title: verse.title,
        surah: verse.surah,
        ayah: verse.ayah,
        slug: verse.slug,
        lesson: verse.lesson,
        reflection: verse.reflection,
        url: `${siteMetadata.siteUrl}/verses/${verse.slug}`,
      }

      // Send emails
      let successCount = 0
      let failureCount = 0

      for (const subscription of subscribersToEmail) {
        try {
          const success = await this.emailService.sendVerseNotification(
            subscription,
            verseEmailData,
            'weekly_reminder'
          )

          if (success) {
            successCount++
            this.subscriptionManager.updateLastEmailSent(subscription.id)
          } else {
            failureCount++
          }

          // Add delay to avoid overwhelming email service
          await new Promise((resolve) => setTimeout(resolve, 100))
        } catch (error) {
          console.error(`Failed to send email to ${subscription.email}:`, error)
          failureCount++
        }
      }

      return {
        success: true,
        sent: successCount,
        failed: failureCount,
        totalSubscriptions: subscriptions.length,
        verse: {
          title: verse.title,
          slug: verse.slug,
        },
      }
    } catch (error) {
      console.error('Error sending weekly reminders:', error)
      return {
        success: false,
        sent: 0,
        failed: 0,
        totalSubscriptions: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  // Get notification statistics
  getNotificationStats() {
    return this.subscriptionManager.getSubscriptionStats()
  }

  // Get recent email logs
  getRecentEmailLogs(limit: number = 50) {
    return this.emailLogger.getEmailLogs(undefined, limit)
  }
}
