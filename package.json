{"name": "tailwind-nextjs-starter-blog", "version": "2.4.0", "private": true, "scripts": {"start": "next dev", "dev": "cross-env INIT_CWD=$PWD next dev", "build": "cross-env INIT_CWD=$PWD next build --no-lint && cross-env NODE_OPTIONS='--experimental-json-modules' node ./scripts/postbuild.mjs", "serve": "next start", "analyze": "cross-env ANALYZE=true next build", "lint": "next lint --fix --dir pages --dir app --dir components --dir lib --dir layouts --dir scripts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prepare": "husky"}, "dependencies": {"@headlessui/react": "2.2.0", "@heroicons/react": "^2.2.0", "@next/bundle-analyzer": "15.2.4", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/postcss": "^4.0.5", "@tailwindcss/typography": "^0.5.15", "@types/better-sqlite3": "^7.6.13", "@types/nodemailer": "^7.0.1", "@types/uuid": "^10.0.0", "better-sqlite3": "^12.2.0", "body-scroll-lock": "^4.0.0-beta.0", "contentlayer2": "0.5.5", "esbuild": "0.25.2", "github-slugger": "^2.0.0", "gray-matter": "^4.0.2", "hast-util-from-html-isomorphic": "^2.0.0", "image-size": "2.0.1", "next": "15.2.4", "next-contentlayer2": "0.5.5", "next-themes": "^0.4.6", "nodemailer": "^7.0.5", "pliny": "0.4.1", "postcss": "^8.4.24", "react": "19.0.0", "react-dom": "19.0.0", "reading-time": "1.5.0", "rehype-autolink-headings": "^7.1.0", "rehype-citation": "^2.3.0", "rehype-katex": "^7.0.0", "rehype-katex-notranslate": "^1.1.4", "rehype-preset-minify": "7.0.0", "rehype-prism-plus": "^2.0.0", "rehype-slug": "^6.0.0", "remark": "^15.0.0", "remark-gfm": "^4.0.0", "remark-github-blockquote-alert": "^1.2.1", "remark-math": "^6.0.0", "tailwindcss": "^4.0.5", "unist-util-visit": "^5.0.0", "uuid": "^11.1.0", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.16.0", "@svgr/webpack": "^8.0.1", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/mdx": "^2.0.12", "@types/react": "^19.0.8", "@typescript-eslint/eslint-plugin": "^8.12.0", "@typescript-eslint/parser": "^8.12.0", "cross-env": "^7.0.3", "eslint": "^9.14.0", "eslint-config-next": "15.2.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.0", "globals": "^15.12.0", "husky": "^9.0.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "lint-staged": "^13.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "^5.1.3"}, "packageManager": "yarn@3.6.1"}