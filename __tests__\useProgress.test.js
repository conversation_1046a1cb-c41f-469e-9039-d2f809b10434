import { renderHook, act } from '@testing-library/react'
import { useProgress } from '@/hooks/useProgress'

describe('useProgress Hook', () => {
  const testSlug = 'test-verse'

  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear()
  })

  it('should initialize with isDone false when no data in localStorage', () => {
    const { result } = renderHook(() => useProgress(testSlug))

    expect(result.current.isDone).toBe(false)
    expect(result.current.isLoaded).toBe(true)
  })

  it('should initialize with isDone true when localStorage has true value', () => {
    localStorage.setItem(`verse_done_${testSlug}`, 'true')

    const { result } = renderHook(() => useProgress(testSlug))

    expect(result.current.isDone).toBe(true)
  })

  it('should mark verse as done', () => {
    const { result } = renderHook(() => useProgress(testSlug))

    act(() => {
      result.current.markDone()
    })

    expect(result.current.isDone).toBe(true)
    expect(localStorage.setItem).toHaveBeenCalledWith(`verse_done_${testSlug}`, 'true')
  })

  it('should mark verse as undone', () => {
    localStorage.setItem(`verse_done_${testSlug}`, 'true')
    const { result } = renderHook(() => useProgress(testSlug))

    act(() => {
      result.current.markUndone()
    })

    expect(result.current.isDone).toBe(false)
    expect(localStorage.setItem).toHaveBeenCalledWith(`verse_done_${testSlug}`, 'false')
  })

  it('should toggle done state', () => {
    const { result } = renderHook(() => useProgress(testSlug))

    // Initially false, should toggle to true
    act(() => {
      result.current.toggleDone()
    })

    expect(result.current.isDone).toBe(true)

    // Now true, should toggle to false
    act(() => {
      result.current.toggleDone()
    })

    expect(result.current.isDone).toBe(false)
  })

  it('should reset progress', () => {
    localStorage.setItem(`verse_done_${testSlug}`, 'true')
    const { result } = renderHook(() => useProgress(testSlug))

    act(() => {
      result.current.reset()
    })

    expect(result.current.isDone).toBe(false)
    expect(localStorage.removeItem).toHaveBeenCalledWith(`verse_done_${testSlug}`)
  })

  it('should get all completed verses', () => {
    // Mock localStorage with multiple completed verses
    localStorage.setItem('verse_done_verse1', 'true')
    localStorage.setItem('verse_done_verse2', 'true')
    localStorage.setItem('verse_done_verse3', 'false')
    localStorage.setItem('other_key', 'value')

    // Mock localStorage.length and key method
    Object.defineProperty(localStorage, 'length', { value: 4 })
    localStorage.key = jest
      .fn()
      .mockReturnValueOnce('verse_done_verse1')
      .mockReturnValueOnce('verse_done_verse2')
      .mockReturnValueOnce('verse_done_verse3')
      .mockReturnValueOnce('other_key')

    const { result } = renderHook(() => useProgress(testSlug))

    const completed = result.current.getAllCompleted()

    expect(completed).toEqual(['verse1', 'verse2'])
  })

  it('should calculate stats correctly', () => {
    localStorage.setItem('verse_done_verse1', 'true')
    localStorage.setItem('verse_done_verse2', 'true')

    Object.defineProperty(localStorage, 'length', { value: 2 })
    localStorage.key = jest
      .fn()
      .mockReturnValueOnce('verse_done_verse1')
      .mockReturnValueOnce('verse_done_verse2')

    const { result } = renderHook(() => useProgress(testSlug))

    const stats = result.current.getStats(10)

    expect(stats).toEqual({
      completed: 2,
      total: 10,
      percentage: 20,
      completedSlugs: ['verse1', 'verse2'],
    })
  })
})
