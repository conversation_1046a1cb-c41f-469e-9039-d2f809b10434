import nodemailer from 'nodemailer'
import { EmailSubscription, EmailLogger } from './database'
import siteMetadata from '@/data/siteMetadata'
import { SubscriptionManager } from './database'

// Email configuration
interface EmailConfig {
  host: string
  port: number
  secure: boolean
  auth: {
    user: string
    pass: string
  }
}

// Email templates
interface EmailTemplate {
  subject: string
  html: string
  text: string
}

// Verse data for email
interface VerseEmailData {
  title: string
  surah: string
  ayah: number
  slug: string
  lesson: string
  reflection: string
  url: string
}

export class EmailService {
  private transporter: nodemailer.Transporter
  private logger: EmailLogger

  constructor() {
    this.logger = new EmailLogger()
    this.transporter = this.createTransporter()
  }

  private createTransporter(): nodemailer.Transporter {
    // Use environment variables for email configuration
    const config: EmailConfig = {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER || '',
        pass: process.env.SMTP_PASS || '',
      },
    }

    // Debug logging (remove in production)
    console.log('SMTP Configuration:', {
      host: config.host,
      port: config.port,
      secure: config.secure,
      user: config.auth.user ? '***configured***' : 'MISSING',
      pass: config.auth.pass ? '***configured***' : 'MISSING',
    })

    if (!config.auth.user || !config.auth.pass) {
      console.error('SMTP credentials are missing!')
      console.error('SMTP_USER:', process.env.SMTP_USER ? 'set' : 'NOT SET')
      console.error('SMTP_PASS:', process.env.SMTP_PASS ? 'set' : 'NOT SET')
    }

    return nodemailer.createTransport(config)
  }

  // Send confirmation email
  async sendConfirmationEmail(subscription: EmailSubscription): Promise<boolean> {
    try {
      const confirmUrl = `${siteMetadata.siteUrl}/api/email/confirm?token=${subscription.confirmationToken}`
      const template = this.getConfirmationTemplate(subscription.language, confirmUrl)

      await this.transporter.sendMail({
        from: `"${siteMetadata.title}" <${process.env.SMTP_USER}>`,
        to: subscription.email,
        subject: template.subject,
        html: template.html,
        text: template.text,
      })

      // Update the lastEmailSent field
      const subscriptionManager = new SubscriptionManager()
      subscriptionManager.updateLastEmailSent(subscription.id)

      return true
    } catch (error) {
      console.error('Failed to send confirmation email:', error)
      return false
    }
  }

  // Send verse notification email
  async sendVerseNotification(
    subscription: EmailSubscription,
    verse: VerseEmailData,
    emailType: 'new_verse' | 'daily_reminder' | 'weekly_reminder'
  ): Promise<boolean> {
    try {
      const unsubscribeUrl = `${siteMetadata.siteUrl}/api/email/unsubscribe?token=${subscription.unsubscribeToken}`
      const template = this.getVerseTemplate(
        subscription.language,
        verse,
        unsubscribeUrl,
        emailType
      )

      await this.transporter.sendMail({
        from: `"${siteMetadata.title}" <${process.env.SMTP_USER}>`,
        to: subscription.email,
        subject: template.subject,
        html: template.html,
        text: template.text,
      })

      this.logger.logEmail(subscription.id, verse.slug, emailType, true)
      return true
    } catch (error) {
      console.error('Failed to send verse notification:', error)
      this.logger.logEmail(
        subscription.id,
        verse.slug,
        emailType,
        false,
        error instanceof Error ? error.message : 'Unknown error'
      )
      return false
    }
  }

  // Get confirmation email template
  private getConfirmationTemplate(language: 'ar' | 'en', confirmUrl: string): EmailTemplate {
    if (language === 'en') {
      return {
        subject: 'Confirm Your Subscription - Daily Verses',
        html: this.getConfirmationHtmlEn(confirmUrl),
        text: `Please confirm your subscription by clicking this link: ${confirmUrl}`,
      }
    }

    return {
      subject: 'تأكيد الاشتراك - آيات يومية',
      html: this.getConfirmationHtmlAr(confirmUrl),
      text: `يرجى تأكيد اشتراكك بالنقر على هذا الرابط: ${confirmUrl}`,
    }
  }

  // Get verse notification template
  private getVerseTemplate(
    language: 'ar' | 'en',
    verse: VerseEmailData,
    unsubscribeUrl: string,
    emailType: 'new_verse' | 'daily_reminder' | 'weekly_reminder'
  ): EmailTemplate {
    if (language === 'en') {
      return {
        subject: `${emailType === 'new_verse' ? 'New Verse' : emailType === 'daily_reminder' ? 'Daily Reminder' : 'Weekly Reminder'}: ${verse.title}`,
        html: this.getVerseHtmlEn(verse, unsubscribeUrl, emailType),
        text: `${verse.title}\n\n${verse.surah} - Verse ${verse.ayah}\n\nLesson: ${verse.lesson}\n\nReflection: ${verse.reflection}\n\nRead more: ${verse.url}\n\nUnsubscribe: ${unsubscribeUrl}`,
      }
    }

    const typeText =
      emailType === 'new_verse'
        ? 'آية جديدة'
        : emailType === 'daily_reminder'
          ? 'تذكير يومي'
          : 'تذكير أسبوعي'
    return {
      subject: `${typeText}: ${verse.title}`,
      html: this.getVerseHtmlAr(verse, unsubscribeUrl, emailType),
      text: `${verse.title}\n\n${verse.surah} - الآية ${verse.ayah}\n\nالدرس: ${verse.lesson}\n\nالتأمل: ${verse.reflection}\n\nاقرأ المزيد: ${verse.url}\n\nإلغاء الاشتراك: ${unsubscribeUrl}`,
    }
  }

  // Arabic confirmation HTML template
  private getConfirmationHtmlAr(confirmUrl: string): string {
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تأكيد الاشتراك</title>
        <style>
          body { font-family: 'Cairo', Arial, sans-serif; line-height: 1.6; color: #333; direction: rtl; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .button { display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${siteMetadata.title}</h1>
            <p>تأكيد الاشتراك في النشرة البريدية</p>
          </div>
          <div class="content">
            <h2>مرحباً بك!</h2>
            <p>شكراً لك على الاشتراك في نشرتنا البريدية للآيات اليومية. لتأكيد اشتراكك، يرجى النقر على الزر أدناه:</p>
            <div style="text-align: center;">
              <a href="${confirmUrl}" class="button">تأكيد الاشتراك</a>
            </div>
            <p>إذا لم تقم بالاشتراك، يمكنك تجاهل هذا البريد الإلكتروني.</p>
          </div>
          <div class="footer">
            <p>© ${new Date().getFullYear()} ${siteMetadata.title}</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  // English confirmation HTML template
  private getConfirmationHtmlEn(confirmUrl: string): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Confirm Subscription</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .button { display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${siteMetadata.title}</h1>
            <p>Email Subscription Confirmation</p>
          </div>
          <div class="content">
            <h2>Welcome!</h2>
            <p>Thank you for subscribing to our daily verses newsletter. To confirm your subscription, please click the button below:</p>
            <div style="text-align: center;">
              <a href="${confirmUrl}" class="button">Confirm Subscription</a>
            </div>
            <p>If you didn't subscribe, you can safely ignore this email.</p>
          </div>
          <div class="footer">
            <p>© ${new Date().getFullYear()} ${siteMetadata.title}</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  // Arabic verse HTML template
  private getVerseHtmlAr(verse: VerseEmailData, unsubscribeUrl: string, emailType: string): string {
    const typeText =
      emailType === 'new_verse'
        ? 'آية جديدة'
        : emailType === 'daily_reminder'
          ? 'تذكير يومي'
          : 'تذكير أسبوعي'

    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${verse.title}</title>
        <style>
          body { font-family: 'Cairo', Arial, sans-serif; line-height: 1.8; color: #333; direction: rtl; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .verse-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; color: #2d3748; }
          .verse-info { color: #667eea; font-weight: bold; margin-bottom: 20px; }
          .lesson { background: #e6fffa; padding: 20px; border-right: 4px solid #38b2ac; margin: 20px 0; border-radius: 5px; }
          .reflection { background: #fef5e7; padding: 20px; border-right: 4px solid #ed8936; margin: 20px 0; border-radius: 5px; }
          .button { display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          .unsubscribe { text-align: center; margin-top: 20px; }
          .unsubscribe a { color: #999; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${siteMetadata.title}</h1>
            <p>${typeText}</p>
          </div>
          <div class="content">
            <div class="verse-title">${verse.title}</div>
            <div class="verse-info">${verse.surah} - الآية ${verse.ayah}</div>
            
            <div class="lesson">
              <h3>💡 الدرس العملي</h3>
              <p>${verse.lesson}</p>
            </div>
            
            <div class="reflection">
              <h3>🤔 للتأمل</h3>
              <p>${verse.reflection}</p>
            </div>
            
            <div style="text-align: center;">
              <a href="${verse.url}" class="button">اقرأ المقال كاملاً</a>
            </div>
          </div>
          <div class="footer">
            <p>© ${new Date().getFullYear()} ${siteMetadata.title}</p>
            <div class="unsubscribe">
              <a href="${unsubscribeUrl}">إلغاء الاشتراك</a>
            </div>
          </div>
        </div>
      </body>
      </html>
    `
  }

  // English verse HTML template
  private getVerseHtmlEn(verse: VerseEmailData, unsubscribeUrl: string, emailType: string): string {
    const typeText =
      emailType === 'new_verse'
        ? 'New Verse'
        : emailType === 'daily_reminder'
          ? 'Daily Reminder'
          : 'Weekly Reminder'

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${verse.title}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .verse-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; color: #2d3748; }
          .verse-info { color: #667eea; font-weight: bold; margin-bottom: 20px; }
          .lesson { background: #e6fffa; padding: 20px; border-left: 4px solid #38b2ac; margin: 20px 0; border-radius: 5px; }
          .reflection { background: #fef5e7; padding: 20px; border-left: 4px solid #ed8936; margin: 20px 0; border-radius: 5px; }
          .button { display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          .unsubscribe { text-align: center; margin-top: 20px; }
          .unsubscribe a { color: #999; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${siteMetadata.title}</h1>
            <p>${typeText}</p>
          </div>
          <div class="content">
            <div class="verse-title">${verse.title}</div>
            <div class="verse-info">${verse.surah} - Verse ${verse.ayah}</div>
            
            <div class="lesson">
              <h3>💡 Practical Lesson</h3>
              <p>${verse.lesson}</p>
            </div>
            
            <div class="reflection">
              <h3>🤔 For Reflection</h3>
              <p>${verse.reflection}</p>
            </div>
            
            <div style="text-align: center;">
              <a href="${verse.url}" class="button">Read Full Article</a>
            </div>
          </div>
          <div class="footer">
            <p>© ${new Date().getFullYear()} ${siteMetadata.title}</p>
            <div class="unsubscribe">
              <a href="${unsubscribeUrl}">Unsubscribe</a>
            </div>
          </div>
        </div>
      </body>
      </html>
    `
  }
}
