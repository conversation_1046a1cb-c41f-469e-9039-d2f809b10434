'use client'

import { useState } from 'react'
import { useChallengeProgress } from '@/hooks/useProgress'
import { getAllChallenges, challengeCategories } from '@/data/challenges'
import { StarIcon, PlayIcon, ClockIcon } from '@heroicons/react/24/outline'
import { useTranslation } from '@/hooks/useTranslation'

interface ChallengeEnrollmentProps {
  verseCategory?: string
  onEnroll?: (challengeId: string) => void
}

export default function ChallengeEnrollment({ verseCategory, onEnroll }: ChallengeEnrollmentProps) {
  const { t } = useTranslation()
  const [selectedChallenge, setSelectedChallenge] = useState<string | null>(null)
  const { currentChallenge, startChallenge } = useChallengeProgress()

  const allChallenges = getAllChallenges()

  // Filter challenges by verse category if provided
  const availableChallenges = verseCategory
    ? allChallenges.filter((challenge) => challenge.category === verseCategory)
    : allChallenges

  const handleStartChallenge = (challengeId: string) => {
    const result = startChallenge(challengeId)
    if (result && onEnroll) {
      onEnroll(challengeId)
    }
  }

  // Don't show enrollment if user already has an active challenge
  if (currentChallenge?.isActive) {
    return null
  }

  return (
    <div className="rounded-lg border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-6 dark:border-blue-700 dark:from-blue-900/20 dark:to-indigo-900/20">
      <div className="mb-4">
        <h3 className="mb-2 flex items-center text-lg font-semibold text-gray-900 dark:text-gray-100">
          <StarIcon className="ml-2 h-5 w-5 text-blue-600 dark:text-blue-400" />
          {t('challenges.startPersonalJourney')}
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">{t('challenges.joinChallenge')}</p>
      </div>

      <div className="space-y-3">
        {availableChallenges.map((challenge) => (
          <div
            key={challenge.id}
            className={`cursor-pointer rounded-lg border p-4 transition-all ${
              selectedChallenge === challenge.id
                ? 'border-blue-500 bg-blue-50 dark:border-blue-400 dark:bg-blue-900/30'
                : 'border-gray-200 bg-white hover:border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:hover:border-gray-500'
            }`}
            role="button"
            tabIndex={0}
            onClick={() => setSelectedChallenge(challenge.id)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault()
                setSelectedChallenge(challenge.id)
              }
            }}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center">
                  <span className="text-lg">{challenge.icon}</span>
                  <h4 className="mr-2 font-medium text-gray-900 dark:text-gray-100">
                    {challenge.title}
                  </h4>
                </div>
                <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                  {challenge.description}
                </p>
                <div className="mt-2 flex items-center text-xs text-gray-500 dark:text-gray-400">
                  <ClockIcon className="ml-1 h-3 w-3" />
                  <span>
                    {challenge.duration} {t('challenges.days')}
                  </span>
                  <span className="mr-2">•</span>
                  <span
                    className={`rounded-full px-2 py-1 text-xs ${
                      challenge.color === 'emerald'
                        ? 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-400'
                        : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                    }`}
                  >
                    {challengeCategories[challenge.category]?.name}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {selectedChallenge && (
        <div className="mt-4 flex justify-center">
          <button
            onClick={() => handleStartChallenge(selectedChallenge)}
            className="flex items-center rounded-lg bg-blue-600 px-6 py-2 font-medium text-white transition-colors hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
          >
            <PlayIcon className="ml-2 h-4 w-4" />
            {t('challenges.startChallenge')}
          </button>
        </div>
      )}
    </div>
  )
}
