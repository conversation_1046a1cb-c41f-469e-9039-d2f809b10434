import Database from 'better-sqlite3'
import { v4 as uuidv4 } from 'uuid'
import path from 'path'

// Database types
export interface EmailSubscription {
  id: string
  email: string
  frequency: 'daily' | 'weekly'
  isActive: boolean
  language: 'ar' | 'en'
  subscribedAt: string
  lastEmailSent?: string
  unsubscribeToken: string
  confirmationToken?: string
  isConfirmed: boolean
}

export interface EmailLog {
  id: string
  subscriptionId: string
  verseSlug: string
  sentAt: string
  emailType: 'new_verse' | 'daily_reminder' | 'weekly_reminder'
  success: boolean
  errorMessage?: string
}

// Database connection
let db: Database.Database | null = null

function getDatabase(): Database.Database {
  if (!db) {
    const dbPath =
      process.env.NODE_ENV === 'production'
        ? '/tmp/subscriptions.db'
        : path.join(process.cwd(), 'data', 'subscriptions.db')

    db = new Database(dbPath)
    initializeDatabase(db)
  }
  return db
}

function initializeDatabase(database: Database.Database) {
  // Create subscriptions table
  database.exec(`
    CREATE TABLE IF NOT EXISTS subscriptions (
      id TEXT PRIMARY KEY,
      email TEXT UNIQUE NOT NULL,
      frequency TEXT NOT NULL CHECK (frequency IN ('daily', 'weekly')),
      isActive INTEGER NOT NULL DEFAULT 1,
      language TEXT NOT NULL DEFAULT 'ar' CHECK (language IN ('ar', 'en')),
      subscribedAt TEXT NOT NULL,
      lastEmailSent TEXT,
      unsubscribeToken TEXT UNIQUE NOT NULL,
      confirmationToken TEXT,
      isConfirmed INTEGER NOT NULL DEFAULT 0
    )
  `)

  // Create email logs table
  database.exec(`
    CREATE TABLE IF NOT EXISTS email_logs (
      id TEXT PRIMARY KEY,
      subscriptionId TEXT NOT NULL,
      verseSlug TEXT NOT NULL,
      sentAt TEXT NOT NULL,
      emailType TEXT NOT NULL CHECK (emailType IN ('new_verse', 'daily_reminder', 'weekly_reminder')),
      success INTEGER NOT NULL,
      errorMessage TEXT,
      FOREIGN KEY (subscriptionId) REFERENCES subscriptions (id)
    )
  `)

  // Create indexes for better performance
  database.exec(`
    CREATE INDEX IF NOT EXISTS idx_subscriptions_email ON subscriptions (email);
    CREATE INDEX IF NOT EXISTS idx_subscriptions_active ON subscriptions (isActive);
    CREATE INDEX IF NOT EXISTS idx_subscriptions_frequency ON subscriptions (frequency);
    CREATE INDEX IF NOT EXISTS idx_email_logs_subscription ON email_logs (subscriptionId);
    CREATE INDEX IF NOT EXISTS idx_email_logs_sent_at ON email_logs (sentAt);
  `)
}

// Subscription management functions
export class SubscriptionManager {
  private db: Database.Database

  constructor() {
    this.db = getDatabase()
  }

  // Create a new subscription or return existing unconfirmed one
  createSubscription(
    email: string,
    frequency: 'daily' | 'weekly',
    language: 'ar' | 'en' = 'ar'
  ): EmailSubscription {
    const subscription: EmailSubscription = {
      id: uuidv4(),
      email: email.toLowerCase().trim(),
      frequency,
      isActive: true,
      language,
      subscribedAt: new Date().toISOString(),
      unsubscribeToken: uuidv4(),
      confirmationToken: uuidv4(),
      isConfirmed: false,
    }

    const stmt = this.db.prepare(`
      INSERT INTO subscriptions (
        id, email, frequency, isActive, language, subscribedAt, 
        unsubscribeToken, confirmationToken, isConfirmed
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)

    try {
      stmt.run(
        subscription.id,
        subscription.email,
        subscription.frequency,
        subscription.isActive ? 1 : 0,
        subscription.language,
        subscription.subscribedAt,
        subscription.unsubscribeToken,
        subscription.confirmationToken,
        subscription.isConfirmed ? 1 : 0
      )
      return subscription
    } catch (error) {
      if (error instanceof Error && error.message.includes('UNIQUE constraint failed')) {
        // Check if there's an existing unconfirmed subscription
        const existing = this.getSubscriptionByEmail(email)
        if (existing && !existing.isConfirmed) {
          // Regenerate the confirmation token and return the existing subscription
          this.regenerateConfirmationToken(email)
          return existing
        }
        throw new Error('Email already subscribed')
      }
      throw error
    }
  }

  // Get subscription by email
  getSubscriptionByEmail(email: string): EmailSubscription | null {
    const stmt = this.db.prepare('SELECT * FROM subscriptions WHERE email = ?')
    const row = stmt.get(email.toLowerCase().trim()) as any
    return row ? this.mapRowToSubscription(row) : null
  }

  // Get subscription by unsubscribe token
  getSubscriptionByUnsubscribeToken(token: string): EmailSubscription | null {
    const stmt = this.db.prepare('SELECT * FROM subscriptions WHERE unsubscribeToken = ?')
    const row = stmt.get(token) as any
    return row ? this.mapRowToSubscription(row) : null
  }

  // Get subscription by confirmation token
  getSubscriptionByConfirmationToken(token: string): EmailSubscription | null {
    const stmt = this.db.prepare('SELECT * FROM subscriptions WHERE confirmationToken = ?')
    const row = stmt.get(token) as any
    return row ? this.mapRowToSubscription(row) : null
  }

  // Confirm subscription
  confirmSubscription(token: string): boolean {
    const stmt = this.db.prepare(`
      UPDATE subscriptions 
      SET isConfirmed = 1, confirmationToken = NULL 
      WHERE confirmationToken = ?
    `)
    const result = stmt.run(token)
    return result.changes > 0
  }

  // Regenerate confirmation token for unconfirmed subscriptions
  regenerateConfirmationToken(email: string): boolean {
    const stmt = this.db.prepare(`
      UPDATE subscriptions
      SET confirmationToken = ?, isConfirmed = 0, lastEmailSent = ?
      WHERE email = ? AND isConfirmed = 0
    `)
    const result = stmt.run(uuidv4(), new Date().toISOString(), email.toLowerCase().trim())
    return result.changes > 0
  }

  // Update subscription preferences
  updateSubscription(
    email: string,
    updates: Partial<Pick<EmailSubscription, 'frequency' | 'language' | 'isActive'>>
  ): boolean {
    const fields: string[] = []
    const values: any[] = []

    if (updates.frequency) {
      fields.push('frequency = ?')
      values.push(updates.frequency)
    }
    if (updates.language) {
      fields.push('language = ?')
      values.push(updates.language)
    }
    if (updates.isActive !== undefined) {
      fields.push('isActive = ?')
      values.push(updates.isActive ? 1 : 0)
    }

    if (fields.length === 0) return false

    values.push(email.toLowerCase().trim())
    const stmt = this.db.prepare(`UPDATE subscriptions SET ${fields.join(', ')} WHERE email = ?`)
    const result = stmt.run(...values)
    return result.changes > 0
  }

  // Unsubscribe by token
  unsubscribe(token: string): boolean {
    const stmt = this.db.prepare('UPDATE subscriptions SET isActive = 0 WHERE unsubscribeToken = ?')
    const result = stmt.run(token)
    return result.changes > 0
  }

  // Get active subscriptions by frequency
  getActiveSubscriptionsByFrequency(frequency: 'daily' | 'weekly'): EmailSubscription[] {
    const stmt = this.db.prepare(`
      SELECT * FROM subscriptions 
      WHERE isActive = 1 AND isConfirmed = 1 AND frequency = ?
    `)
    const rows = stmt.all(frequency) as any[]
    return rows.map((row) => this.mapRowToSubscription(row))
  }

  // Update last email sent timestamp
  updateLastEmailSent(subscriptionId: string): void {
    const stmt = this.db.prepare('UPDATE subscriptions SET lastEmailSent = ? WHERE id = ?')
    stmt.run(new Date().toISOString(), subscriptionId)
  }

  // Get subscription statistics
  getSubscriptionStats() {
    const totalStmt = this.db.prepare(
      'SELECT COUNT(*) as count FROM subscriptions WHERE isConfirmed = 1'
    )
    const activeStmt = this.db.prepare(
      'SELECT COUNT(*) as count FROM subscriptions WHERE isActive = 1 AND isConfirmed = 1'
    )
    const dailyStmt = this.db.prepare(
      'SELECT COUNT(*) as count FROM subscriptions WHERE frequency = ? AND isActive = 1 AND isConfirmed = 1'
    )
    const weeklyStmt = this.db.prepare(
      'SELECT COUNT(*) as count FROM subscriptions WHERE frequency = ? AND isActive = 1 AND isConfirmed = 1'
    )

    return {
      total: (totalStmt.get() as any).count,
      active: (activeStmt.get() as any).count,
      daily: (dailyStmt.get(['daily']) as any).count,
      weekly: (weeklyStmt.get(['weekly']) as any).count,
    }
  }

  private mapRowToSubscription(row: any): EmailSubscription {
    return {
      id: row.id,
      email: row.email,
      frequency: row.frequency,
      isActive: Boolean(row.isActive),
      language: row.language,
      subscribedAt: row.subscribedAt,
      lastEmailSent: row.lastEmailSent,
      unsubscribeToken: row.unsubscribeToken,
      confirmationToken: row.confirmationToken,
      isConfirmed: Boolean(row.isConfirmed),
    }
  }
}

// Email logging functions
export class EmailLogger {
  private db: Database.Database

  constructor() {
    this.db = getDatabase()
  }

  logEmail(
    subscriptionId: string,
    verseSlug: string,
    emailType: 'new_verse' | 'daily_reminder' | 'weekly_reminder',
    success: boolean,
    errorMessage?: string
  ): void {
    const stmt = this.db.prepare(`
      INSERT INTO email_logs (id, subscriptionId, verseSlug, sentAt, emailType, success, errorMessage)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `)

    stmt.run(
      uuidv4(),
      subscriptionId,
      verseSlug,
      new Date().toISOString(),
      emailType,
      success ? 1 : 0,
      errorMessage
    )
  }

  getEmailLogs(subscriptionId?: string, limit: number = 100): EmailLog[] {
    let query = 'SELECT * FROM email_logs'
    const params: any[] = []

    if (subscriptionId) {
      query += ' WHERE subscriptionId = ?'
      params.push(subscriptionId)
    }

    query += ' ORDER BY sentAt DESC LIMIT ?'
    params.push(limit)

    const stmt = this.db.prepare(query)
    const rows = stmt.all(...params) as any[]

    return rows.map((row) => ({
      id: row.id,
      subscriptionId: row.subscriptionId,
      verseSlug: row.verseSlug,
      sentAt: row.sentAt,
      emailType: row.emailType,
      success: Boolean(row.success),
      errorMessage: row.errorMessage,
    }))
  }
}

// Ensure data directory exists
export function ensureDataDirectory() {
  if (process.env.NODE_ENV !== 'production') {
    const fs = require('fs')
    const dataDir = path.join(process.cwd(), 'data')
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true })
    }
  }
}

// Initialize database on import
ensureDataDirectory()
