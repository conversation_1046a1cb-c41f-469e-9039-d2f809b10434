{"image": "mcr.microsoft.com/devcontainers/javascript-node:20", "hostRequirements": {"memory": "8gb"}, "waitFor": "onCreateCommand", "updateContentCommand": "yarn", "postCreateCommand": "", "postAttachCommand": "yarn dev", "customizations": {"codespaces": {"openFiles": ["data/blog/code-sample.mdx"]}, "vscode": {"settings": {"editor.formatOnSave": true}, "extensions": ["dbaeumer.vscode-eslint", "esbenp.prettier-vscode"]}}, "portsAttributes": {"3000": {"label": "Nextjs server", "onAutoForward": "openPreview"}}, "forwardPorts": [3000]}