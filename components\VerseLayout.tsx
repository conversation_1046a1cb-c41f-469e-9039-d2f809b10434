'use client'

import { ReactNode } from 'react'
import { Metadata } from 'next'
import Link from '@/components/Link'
import Tag from '@/components/Tag'
import ApplyToday from '@/components/ApplyToday'
import ReflectionPrompt from '@/components/ReflectionPrompt'
import VerseChallengeSection from '@/components/VerseChallengeSection'
import { formatDate } from 'pliny/utils/formatDate'
import siteMetadata from '@/data/siteMetadata'
import { useTranslation } from '@/hooks/useTranslation'

interface VerseLayoutProps {
  verse: {
    title: string
    surah: string
    ayah: number
    slug: string
    date: string
    tags?: string[]
    lesson: string
    reflection: string
    ogImage?: string
    lastmod?: string
  }
  children: ReactNode
}

export default function VerseLayout({ verse, children }: VerseLayoutProps) {
  const { t } = useTranslation()
  const { title, surah, ayah, slug, date, tags = [], lesson, reflection, lastmod } = verse

  return (
    <article className="mx-auto max-w-4xl">
      {/* Header */}
      <header className="mb-8 text-center">
        <div className="mb-4">
          <h1 className="mb-2 text-3xl leading-tight font-bold text-gray-900 sm:text-4xl dark:text-gray-100">
            {title}
          </h1>
          <div className="flex items-center justify-center space-x-4 text-lg text-gray-600 dark:text-gray-400">
            <span className="font-medium">{surah}</span>
            <span>•</span>
            <span>
              {t('verse.verse')} {ayah}
            </span>
          </div>
        </div>

        <div className="flex items-center justify-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
          <time dateTime={date}>{formatDate(date, siteMetadata.locale)}</time>
          {lastmod && lastmod !== date && (
            <>
              <span>•</span>
              <span>
                {t('verse.updated')} {formatDate(lastmod, siteMetadata.locale)}
              </span>
            </>
          )}
        </div>

        {/* Tags */}
        {tags.length > 0 && (
          <div className="mt-4 flex flex-wrap justify-center gap-2">
            {tags.map((tag) => (
              <Tag key={tag} text={tag} />
            ))}
          </div>
        )}
      </header>

      {/* Main Content */}
      <div className="prose prose-lg dark:prose-invert mx-auto max-w-none">{children}</div>

      {/* Interactive Components */}
      <div className="mt-12 space-y-8">
        {/* Apply Today Section */}
        <ApplyToday slug={slug} lesson={lesson} reflection={reflection} />

        {/* Reflection Prompt */}
        <ReflectionPrompt reflection={reflection} />

        {/* Challenge Enrollment */}
        <VerseChallengeSection verseCategory={tags[0]} />
      </div>

      {/* Share Section */}
      <div className="mt-12 border-t border-gray-200 pt-8 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {t('verse.shareVerse')}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t('verse.shareDescription')}
            </p>
          </div>

          <div className="flex space-x-3">
            {/* WhatsApp Share */}
            <a
              href={`https://wa.me/?text=${encodeURIComponent(
                `${title} - ${surah} آية ${ayah}\n\n${lesson}\n\n${siteMetadata.siteUrl}/verses/${slug}`
              )}`}
              target="_blank"
              rel="noopener noreferrer"
              className="rounded-lg bg-green-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-green-700"
            >
              واتساب
            </a>

            {/* Twitter Share */}
            <a
              href={`https://twitter.com/intent/tweet?text=${encodeURIComponent(
                `${title} - ${surah} آية ${ayah}\n\n${lesson}`
              )}&url=${encodeURIComponent(`${siteMetadata.siteUrl}/verses/${slug}`)}`}
              target="_blank"
              rel="noopener noreferrer"
              className="rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700"
            >
              تويتر
            </a>

            {/* Copy Link - Note: This would need to be a Client Component for onClick */}
            <span className="rounded-lg bg-gray-600 px-4 py-2 text-sm font-medium text-white">
              نسخ الرابط
            </span>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="mt-8 flex justify-between">
        <Link
          href="/verses"
          className="text-blue-600 transition-colors hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
        >
          ← جميع الآيات
        </Link>
        <Link
          href="/"
          className="text-blue-600 transition-colors hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
        >
          الصفحة الرئيسية →
        </Link>
      </div>
    </article>
  )
}
