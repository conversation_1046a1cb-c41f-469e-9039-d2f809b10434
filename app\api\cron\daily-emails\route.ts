import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Verify the request is from Vercel Cron or authorized source
    const authHeader = request.headers.get('authorization')
    const expectedAuth = process.env.CRON_SECRET || 'your-secret-key'

    if (authHeader !== `Bearer ${expectedAuth}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Call the email notification API to send daily reminders
    const baseUrl = process.env.VERCEL_URL
      ? `https://${process.env.VERCEL_URL}`
      : 'https://quran-verses-gilt.vercel.app'

    const response = await fetch(`${baseUrl}/api/email/notify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${expectedAuth}`,
      },
      body: JSON.stringify({
        type: 'daily_reminder',
      }),
    })

    const result = await response.json()

    if (response.ok) {
      return NextResponse.json({
        success: true,
        message: 'Daily emails sent successfully',
        ...result,
      })
    } else {
      return NextResponse.json(
        { error: 'Failed to send daily emails', details: result },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Daily email cron error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
